using StackExchange.Redis;
using System.Text.Json;
using Microsoft.Extensions.Options;
using WeChatBus.Models;
using WeChatBus.Configuration;

namespace WeChatBus.Services;

/// <summary>
/// Redis数据服务实现
/// </summary>
public class RedisService : IRedisService
{
    private readonly IConnectionMultiplexer _redis;
    private readonly IDatabase _database;
    private readonly ISubscriber _subscriber;
    private readonly ILogger<RedisService> _logger;
    private readonly RedisConfiguration _config;
    private readonly JsonSerializerOptions _jsonOptions;

    // Redis键前缀
    private const string VEHICLE_LOCATION_PREFIX = "vehicle_location:";
    private const string VEHICLE_LIST_KEY = "vehicle_locations";
    private const string STATION_ARRIVAL_PREFIX = "station_arrival:"; // 到站记录前缀

    public RedisService(
        IConnectionMultiplexer redis, 
        IOptions<RedisConfiguration> config,
        ILogger<RedisService> logger)
    {
        _redis = redis;
        _database = redis.GetDatabase();
        _subscriber = redis.GetSubscriber();
        _config = config.Value;
        _logger = logger;
        
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    /// <summary>
    /// 保存车辆位置信息
    /// </summary>
    public async Task SaveVehicleLocationsAsync(List<VehicleLocation> locations)
    {
        if (locations == null || !locations.Any())
        {
            _logger.LogWarning("车辆位置信息列表为空，跳过保存");
            return;
        }

        try
        {
            var batch = _database.CreateBatch();
            var expiration = TimeSpan.FromMinutes(_config.KeyExpirationMinutes);
            var tasks = new List<Task>();
            foreach (var location in locations)
            {
                var key = $"{VEHICLE_LOCATION_PREFIX}{location.CarId}";
                var value = JsonSerializer.Serialize(location, _jsonOptions);
                // 保存单个车辆位置信息
                tasks.Add(batch.StringSetAsync(key, value, expiration));
                // 添加到车辆列表
                tasks.Add(batch.SetAddAsync(VEHICLE_LIST_KEY, location.CarId));
            }
            // 设置车辆列表过期时间
            _ = batch.KeyExpireAsync(VEHICLE_LIST_KEY, expiration);

            batch.Execute();
            await Task.WhenAll(tasks);
            _logger.LogInformation("成功保存 {Count} 个车辆位置信息到Redis", locations.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存车辆位置信息到Redis时发生异常");
            throw;
        }
    }

    /// <summary>
    /// 保存单个车辆位置信息
    /// </summary>
    public async Task SaveVehicleLocationAsync(VehicleLocation location)
    {
        if (location == null)
        {
            _logger.LogWarning("车辆位置信息为空，跳过保存");
            return;
        }

        try
        {
            var expiration = TimeSpan.FromMinutes(_config.KeyExpirationMinutes);
            var key = $"{VEHICLE_LOCATION_PREFIX}{location.CarId}";
            var value = JsonSerializer.Serialize(location, _jsonOptions);
            
            // 使用事务确保数据一致性
            var transaction = _database.CreateTransaction();
            _ = transaction.StringSetAsync(key, value, expiration);
            _ = transaction.SetAddAsync(VEHICLE_LIST_KEY, location.CarId);
            _ = transaction.KeyExpireAsync(VEHICLE_LIST_KEY, expiration);
            
            await transaction.ExecuteAsync();
            _logger.LogDebug("成功保存车辆 {CarId} 位置信息到Redis", location.CarId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存车辆 {CarId} 位置信息到Redis时发生异常", location.CarId);
            throw;
        }
    }

    /// <summary>
    /// 获取车辆位置信息
    /// </summary>
    public async Task<VehicleLocation?> GetVehicleLocationAsync(string carId)
    {
        if (string.IsNullOrEmpty(carId))
        {
            return null;
        }

        try
        {
            var key = $"{VEHICLE_LOCATION_PREFIX}{carId}";
            var value = await _database.StringGetAsync(key);

            if (!value.HasValue)
            {
                return null;
            }

            return JsonSerializer.Deserialize<VehicleLocation>(value!, _jsonOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从Redis获取车辆位置信息时发生异常，车辆ID: {CarId}", carId);
            return null;
        }
    }

    /// <summary>
    /// 获取所有车辆位置信息
    /// </summary>
    public async Task<List<VehicleLocation>> GetAllVehicleLocationsAsync()
    {
        try
        {
            // 获取所有车辆ID
            var carIds = await _database.SetMembersAsync(VEHICLE_LIST_KEY);
            
            if (!carIds.Any())
            {
                return new List<VehicleLocation>();
            }

            var locations = new List<VehicleLocation>();
            var batch = _database.CreateBatch();
            var tasks = new List<Task<RedisValue>>();

            foreach (var carId in carIds)
            {
                var key = $"{VEHICLE_LOCATION_PREFIX}{carId}";
                tasks.Add(batch.StringGetAsync(key));
            }

            batch.Execute();
            var results = await Task.WhenAll(tasks);

            foreach (var result in results)
            {
                if (result.HasValue)
                {
                    try
                    {
                        var location = JsonSerializer.Deserialize<VehicleLocation>(result!, _jsonOptions);
                        if (location != null)
                        {
                            locations.Add(location);
                        }
                    }
                    catch (JsonException ex)
                    {
                        _logger.LogWarning(ex, "反序列化车辆位置信息时发生异常");
                    }
                }
            }
            locations = locations.OrderBy(l => l.CarLine).ToList();
            _logger.LogDebug("从Redis获取到 {Count} 个车辆位置信息", locations.Count);
            return locations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从Redis获取所有车辆位置信息时发生异常");
            return new List<VehicleLocation>();
        }
    }

    /// <summary>
    /// 发布位置更新事件
    /// </summary>
    public async Task PublishLocationUpdateAsync(LocationUpdateEvent updateEvent)
    {
        if (updateEvent == null)
        {
            return;
        }

        try
        {
            var channel = _config.GetFullChannelName(_config.LocationUpdateChannel);
            var message = JsonSerializer.Serialize(updateEvent, _jsonOptions);
            
            var subscriberCount = await _subscriber.PublishAsync(RedisChannel.Literal(channel), message);
            _logger.LogDebug("发布位置更新事件到频道 {Channel}，订阅者数量: {Count}", channel, subscriberCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发布位置更新事件时发生异常");
            throw;
        }
    }

    /// <summary>
    /// 订阅位置更新事件
    /// </summary>
    public async Task SubscribeLocationUpdatesAsync(Action<string, string> onMessage)
    {
        try
        {
            var channel = _config.GetFullChannelName(_config.LocationUpdateChannel);
            
            await _subscriber.SubscribeAsync(RedisChannel.Literal(channel), (ch, message) =>
            {
                try
                {
                    onMessage(ch.ToString(), message.ToString());
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "处理位置更新事件时发生异常");
                }
            });

            _logger.LogInformation("成功订阅位置更新频道: {Channel}", channel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "订阅位置更新事件时发生异常");
            throw;
        }
    }

    /// <summary>
    /// 检查Redis连接状态
    /// </summary>
    public async Task<bool> IsConnectedAsync()
    {
        try
        {
            await _database.PingAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis连接检查失败");
            return false;
        }
    }

    /// <summary>
    /// 清理过期数据
    /// </summary>
    public async Task CleanupExpiredDataAsync()
    {
        try
        {
            // Redis会自动清理过期键，这里可以添加额外的清理逻辑
            _logger.LogDebug("执行Redis数据清理");
            
            // 可以在这里添加自定义的清理逻辑
            // 例如：清理超过一定时间的车辆位置数据
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理Redis过期数据时发生异常");
        }
    }

    /// <summary>
    /// 保存到站状态到缓存（有效期10小时）
    /// </summary>
    public async Task SaveStationArrivalAsync(string carId, string stationId, DateTime arrivalTime)
    {
        if (string.IsNullOrEmpty(carId) || string.IsNullOrEmpty(stationId))
        {
            _logger.LogWarning("车辆ID或站点ID为空，跳过保存到站记录");
            return;
        }

        try
        {
            var arrival = new StationArrival
            {
                CarId = carId,
                StationId = stationId,
                ArrivalTime = arrivalTime,
                // 可以在调用时传递具体坐标，这里先设置默认值
                Latitude = 0,
                Longitude = 0
            };

            var key = $"{STATION_ARRIVAL_PREFIX}{carId}";
            var score = ((DateTimeOffset)arrivalTime).ToUnixTimeSeconds();
            var member = JsonSerializer.Serialize(arrival, _jsonOptions);

            // 使用ZADD命令将到站记录添加到有序集合中，按时间排序
            await _database.SortedSetAddAsync(key, member, score);
            
            // 设置10小时过期时间
            var expiration = TimeSpan.FromHours(10);
            await _database.KeyExpireAsync(key, expiration);

            // 删除超过10小时的记录
            var cutoffTime = ((DateTimeOffset)arrivalTime.AddHours(-10)).ToUnixTimeSeconds();
            await _database.SortedSetRemoveRangeByScoreAsync(key, 0, cutoffTime);

            _logger.LogInformation("保存到站记录: 车辆{CarId} 到达站点{StationId} 时间{ArrivalTime}", 
                carId, stationId, arrivalTime);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存到站记录时发生异常: 车辆{CarId} 站点{StationId}", carId, stationId);
        }
    }

    /// <summary>
    /// 获取车辆到站历史记录
    /// </summary>
    public async Task<List<StationArrival>> GetStationArrivalsAsync(string carId)
    {
        if (string.IsNullOrEmpty(carId))
        {
            return new List<StationArrival>();
        }

        try
        {
            var key = $"{STATION_ARRIVAL_PREFIX}{carId}";
            
            // 获取最近10小时的到站记录（按时间倒序）
            var records = await _database.SortedSetRangeByScoreAsync(key, 
                ((DateTimeOffset)DateTime.UtcNow.AddHours(-10)).ToUnixTimeSeconds(),
                ((DateTimeOffset)DateTime.UtcNow).ToUnixTimeSeconds(),
                order: Order.Descending);

            var arrivals = new List<StationArrival>();
            foreach (var record in records)
            {
                try
                {
                    var arrival = JsonSerializer.Deserialize<StationArrival>(record!, _jsonOptions);
                    if (arrival != null)
                    {
                        arrivals.Add(arrival);
                    }
                }
                catch (JsonException ex)
                {
                    _logger.LogWarning(ex, "反序列化到站记录时发生异常");
                }
            }

            return arrivals;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取车辆到站记录时发生异常: 车辆{CarId}", carId);
            return new List<StationArrival>();
        }
    }

    /// <summary>
    /// 检查车辆是否在指定时间内到过某站
    /// </summary>
    public async Task<bool> HasArrivedAtStationAsync(string carId, string stationId, int withinHours = 1)
    {
        if (string.IsNullOrEmpty(carId) || string.IsNullOrEmpty(stationId))
        {
            return false;
        }

        try
        {
            var arrivals = await GetStationArrivalsAsync(carId);
            var cutoffTime = DateTime.UtcNow.AddHours(-withinHours);
            
            return arrivals.Any(a => a.StationId == stationId && a.ArrivalTime >= cutoffTime);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查车辆到站记录时发生异常: 车辆{CarId} 站点{StationId}", carId, stationId);
            return false;
        }
    }
}

# 微信小程序班车查看系统 Nginx 配置文件
# 用于代理转发第三方接口和我们的后端接口

# 上游服务器配置
upstream kwange_backend {
    server 127.0.0.1:5000;  # 我们的.NET Core后端服务
    keepalive 32;
}

upstream qiye_backend {
    server 121.41.14.18:9999;  # 第三方GPS服务
    keepalive 16;
}

# 主服务器配置
server {
    listen 80;
    server_name your-domain.com;  # 替换为实际域名
    
    # 重定向HTTP到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;  # 替换为实际域名
    
    # SSL证书配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;
    
    # 现代SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 安全头
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # 日志配置
    access_log /var/log/nginx/wechatbus_access.log;
    error_log /var/log/nginx/wechatbus_error.log;
    
    # 通用配置
    client_max_body_size 10M;
    proxy_connect_timeout 30s;
    proxy_send_timeout 30s;
    proxy_read_timeout 30s;
    
    # 我们的后端接口 - 统一前缀 /api/kwange/
    location /api/kwange/ {
        proxy_pass http://kwange_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 添加CORS头
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
        
        # 处理OPTIONS请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
            add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type 'text/plain; charset=utf-8';
            add_header Content-Length 0;
            return 204;
        }
    }
    
    # 第三方接口代理 - 统一前缀 /api/qiye/
    location /api/qiye/ {
        # 移除前缀并转发到第三方服务
        rewrite ^/api/qiye/(.*)$ /gps-web/h5/$1 break;
        proxy_pass http://qiye_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $proxy_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 第三方接口可能需要特殊的头部
        proxy_set_header User-Agent "WeChatBus/1.0";
        proxy_set_header Accept "application/json";
        proxy_set_header Content-Type "application/json";
        
        # 添加CORS头
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
        
        # 处理OPTIONS请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
            add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type 'text/plain; charset=utf-8';
            add_header Content-Length 0;
            return 204;
        }
        
        # 超时配置（第三方接口可能较慢）
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 静态文件服务（如果需要）
    location /static/ {
        alias /var/www/wechatbus/static/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # 默认路由
    location / {
        return 404 "Not Found";
    }
}

# 负载均衡配置（如果有多个后端实例）
# upstream kwange_backend_cluster {
#     least_conn;
#     server 127.0.0.1:5000 weight=1 max_fails=3 fail_timeout=30s;
#     server 127.0.0.1:5001 weight=1 max_fails=3 fail_timeout=30s;
#     keepalive 32;
# }

# 限流配置
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=qiye:10m rate=5r/s;

# 在server块中使用限流
# location /api/kwange/ {
#     limit_req zone=api burst=20 nodelay;
#     # ... 其他配置
# }

# location /api/qiye/ {
#     limit_req zone=qiye burst=10 nodelay;
#     # ... 其他配置
# }

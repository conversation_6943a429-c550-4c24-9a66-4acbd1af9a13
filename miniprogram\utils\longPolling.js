/**
 * 长轮询服务
 * 用于替代WebSocket实现实时数据推送
 */

class LongPollingService {
  constructor() {
    this.isPolling = false
    this.pollingTimer = null
    this.retryCount = 0
    this.maxRetryCount = 3
    this.retryDelay = 1000 // 1秒
    this.pollTimeout = 30000 // 增加到30秒超时，减少重连频率
    this.callbacks = new Set()
    // 延迟获取baseUrl，避免在App初始化前调用
    this.baseUrl = null
  }

  // 获取baseUrl的方法
  getBaseUrl() {
    if (!this.baseUrl) {
      try {
        const app = getApp()
        if (app && app.globalData && app.globalData.baseUrl) {
          this.baseUrl = app.globalData.baseUrl.replace('/api', '') // 移除/api后缀
        } else {
          // 如果无法获取，使用默认值
         // this.baseUrl = 'https://microcloud.iprima.com.cn/api/wechatbus'
         this.baseUrl = 'http://localhost:5000'
          console.warn('无法获取app.globalData.baseUrl，使用默认值')
        }
      } catch (error) {
        console.error('获取baseUrl失败:', error)
       // this.baseUrl = 'https://microcloud.iprima.com.cn/api/wechatbus'
       this.baseUrl = 'http://localhost:5000'
      }
    }
    return this.baseUrl
  }

  /**
   * 开始长轮询
   */
  start() {
    if (this.isPolling) {
      console.log('长轮询已在运行中，跳过启动')
      return
    }

    this.isPolling = true
    this.retryCount = 0
    console.log('开始长轮询服务')
    this._poll()
  }

  /**
   * 停止长轮询
   */
  stop() {
    this.isPolling = false
    if (this.pollingTimer) {
      clearTimeout(this.pollingTimer)
      this.pollingTimer = null
    }
    console.log('停止长轮询服务')
  }

  /**
   * 添加数据回调
   */
  onData(callback) {
    this.callbacks.add(callback)
  }

  /**
   * 移除数据回调
   */
  offData(callback) {
    this.callbacks.delete(callback)
  }

  /**
   * 执行长轮询请求
   */
  _poll() {
    if (!this.isPolling) {
      return
    }

    console.log('发起长轮询请求...')

    wx.request({
      url: `${this.getBaseUrl()}/api/longpolling/poll?timeout=25`, // 增加服务器端超时到25秒
      method: 'GET',
      timeout: this.pollTimeout,
      success: (res) => {
        this.retryCount = 0 // 重置重试计数

        if (res.statusCode === 200 && res.data && res.data.success) {
          // 收到数据，通知所有回调
          const locationData = res.data.data
          console.log('长轮询收到数据:', locationData)
          
          this.callbacks.forEach(callback => {
            try {
              callback(locationData)
            } catch (error) {
              console.error('执行长轮询回调时发生错误:', error)
            }
          })
        } else if (res.statusCode === 204) {
          // 超时，没有新数据  
          console.log('长轮询超时，没有新数据')
        } else {
          console.warn('长轮询响应异常:', res)
        }

        // 立即发起下一次轮询
        this._scheduleNextPoll(0)
      },
      fail: (error) => {
        console.error('长轮询请求失败:', error)
        this._handleError()
      }
    })
  }

  /**
   * 处理错误和重试
   */
  _handleError() {
    if (!this.isPolling) {
      return
    }

    this.retryCount++
    
    if (this.retryCount <= this.maxRetryCount) {
      const delay = this.retryDelay * this.retryCount
      console.log(`长轮询重试 ${this.retryCount}/${this.maxRetryCount}，${delay}ms后重试`)
      this._scheduleNextPoll(delay)
    } else {
      console.error('长轮询达到最大重试次数，停止轮询')
      this.stop()
      
      // 通知错误
      this.callbacks.forEach(callback => {
        try {
          callback(null, new Error('长轮询连接失败'))
        } catch (error) {
          console.error('执行长轮询错误回调时发生错误:', error)
        }
      })
    }
  }

  /**
   * 调度下一次轮询
   */
  _scheduleNextPoll(delay = 0) {
    if (!this.isPolling) {
      return
    }

    this.pollingTimer = setTimeout(() => {
      this._poll()
    }, delay)
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      isPolling: this.isPolling,
      retryCount: this.retryCount,
      callbackCount: this.callbacks.size
    }
  }
}

// 创建全局实例
const longPollingService = new LongPollingService()

module.exports = {
  LongPollingService,
  longPollingService
}

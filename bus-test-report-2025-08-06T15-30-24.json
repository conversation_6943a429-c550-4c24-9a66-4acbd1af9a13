{"report": {"testStartTime": "2025/8/6 23:27:16", "testDuration": "3分8秒", "totalUpdates": 0, "successfulUpdates": 0, "failedUpdates": 0, "successRate": "0%", "stationsVisited": 0, "cyclesCompleted": 0, "averageUpdateInterval": 0}, "logs": "[清空]\n日志已清空\n\n[23:27:16]\n🚀 开始智能自动测试 - 站点循环模式\n\n[23:27:16]\n📋 测试计划: 按顺序访问所有站点，触发50米到站检测，验证后端业务逻辑\n\n[23:27:16]\n🎯 自动测试步骤 1: 前往 总站(班车总站)\n\n[23:27:16]\n📍 移动到总站: (24.58, 118.1)\n\n[23:27:16]\n✅ 位置数据发送成功: 车辆1162\n\n[23:27:16]\n📍 坐标: (24.580000, 118.100000)\n\n[23:27:16]\n🏠 地址: 福建省厦门市集美区班车总站停车场\n\n[23:27:16]\n🔄 后端计算: 总站(班车总站) → 嘉庚体育馆站\n\n[23:27:16]\n📊 状态: 已到站\n\n[23:27:16]\n✅ 50米到站检测成功: 总站(班车总站)\n\n[23:27:16]\n📋 已过站点更新: 0个站点\n\n[23:27:16]\n✅ 已过站点记录验证通过\n\n[23:27:16]\n🔄 Redis状态更新: 站点索引0\n\n[23:27:16]\n📋 已过站点: 0个\n\n[23:27:21]\n🎯 自动测试步骤 2: 前往 嘉庚体育馆站\n\n[23:27:21]\n📍 移动到 嘉庚体育馆站: (24.587743, 118.106940) 距离: 1111米\n\n[23:27:21]\n✅ 位置数据发送成功: 车辆1162\n\n[23:27:21]\n📍 坐标: (24.587743, 118.106940)\n\n[23:27:21]\n🏠 地址: 福建省厦门市集美区嘉庚路199号嘉庚体育馆\n\n[23:27:21]\n🔄 后端计算: 嘉庚体育馆站 → 集美旧厂区站\n\n[23:27:21]\n📊 状态: 已到站\n\n[23:27:21]\n✅ 50米到站检测成功: 嘉庚体育馆站\n\n[23:27:21]\n📋 已过站点更新: 1个站点\n\n[23:27:21]\n✅ 已过站点记录验证通过\n\n[23:27:21]\n🔄 Redis状态更新: 站点索引1\n\n[23:27:21]\n📋 已过站点: 1个\n\n[23:27:22]\n💓 自动测试心跳: 车辆1162 第1轮测试进行中\n\n[23:27:24]\n🧪 开始多用户并发测试...\n\n[23:27:24]\n👤 用户1 开始会话\n\n[23:27:24]\n👤 用户2 开始会话\n\n[23:27:24]\n👤 用户3 开始会话\n\n[23:27:24]\n👤 用户1 获取到5个车辆位置\n\n[23:27:24]\n👤 用户2 获取到5个车辆位置\n\n[23:27:24]\n👤 用户3 获取到5个车辆位置\n\n[23:27:24]\n👤 用户1 长轮询正常\n\n[23:27:24]\n👤 用户1 会话结束\n\n[23:27:26]\n🎯 自动测试步骤 3: 前往 集美旧厂区站\n\n[23:27:26]\n📍 移动到 集美旧厂区站: (24.596212, 118.097264) 距离: 1358米\n\n[23:27:26]\n✅ 位置数据发送成功: 车辆1162\n\n[23:27:26]\n📍 坐标: (24.596212, 118.097264)\n\n[23:27:26]\n🏠 地址: 福建省厦门市集美区杏林湾路集美旧厂区\n\n[23:27:26]\n🔄 后端计算: 集美旧厂区站 → 霞梧路口站\n\n[23:27:26]\n📊 状态: 已到站\n\n[23:27:26]\n✅ 50米到站检测成功: 集美旧厂区站\n\n[23:27:26]\n📋 已过站点更新: 2个站点\n\n[23:27:26]\n✅ 已过站点记录验证通过\n\n[23:27:26]\n🔄 Redis状态更新: 站点索引2\n\n[23:27:26]\n📋 已过站点: 2个\n\n[23:27:26]\n👤 用户2 长轮询正常\n\n[23:27:26]\n👤 用户2 会话结束\n\n[23:27:29]\n👤 用户3 长轮询正常\n\n[23:27:29]\n👤 用户3 会话结束\n\n[23:27:29]\n✅ 多用户并发测试完成\n\n[23:27:31]\n🎯 自动测试步骤 4: 前往 霞梧路口站\n\n[23:27:31]\n📍 移动到 霞梧路口站: (24.576005, 118.097368) 距离: 2247米\n\n[23:27:31]\n✅ 位置数据发送成功: 车辆1162\n\n[23:27:31]\n📍 坐标: (24.576005, 118.097368)\n\n[23:27:31]\n🏠 地址: 福建省厦门市集美区霞梧路与杏林北路交叉口\n\n[23:27:31]\n🔄 后端计算: 霞梧路口站 → 叶厝站\n\n[23:27:31]\n📊 状态: 已到站\n\n[23:27:31]\n✅ 50米到站检测成功: 霞梧路口站\n\n[23:27:31]\n📋 已过站点更新: 3个站点\n\n[23:27:31]\n✅ 已过站点记录验证通过\n\n[23:27:31]\n🔄 Redis状态更新: 站点索引3\n\n[23:27:31]\n📋 已过站点: 3个\n\n[23:27:36]\n🎯 自动测试步骤 5: 前往 叶厝站\n\n[23:27:36]\n📍 移动到 叶厝站: (24.594311, 118.106687) 距离: 2243米\n\n[23:27:36]\n✅ 位置数据发送成功: 车辆1162\n\n[23:27:36]\n📍 坐标: (24.594311, 118.106687)\n\n[23:27:36]\n🏠 地址: 福建省厦门市集美区叶厝村委会附近\n\n[23:27:36]\n🔄 后端计算: 叶厝站 → 禹州大学城站\n\n[23:27:36]\n📊 状态: 已到站\n\n[23:27:36]\n✅ 50米到站检测成功: 叶厝站\n\n[23:27:36]\n📋 已过站点更新: 4个站点\n\n[23:27:36]\n✅ 已过站点记录验证通过\n\n[23:27:36]\n🔄 Redis状态更新: 站点索引4\n\n[23:27:36]\n📋 已过站点: 4个\n\n[23:27:41]\n🎯 自动测试步骤 6: 前往 禹州大学城站\n\n[23:27:41]\n📍 移动到 禹州大学城站: (24.621916, 118.128037) 距离: 3752米\n\n[23:27:41]\n✅ 位置数据发送成功: 车辆1162\n\n[23:27:41]\n📍 坐标: (24.621916, 118.128037)\n\n[23:27:41]\n🏠 地址: 福建省厦门市集美区禹州大学城商业街\n\n[23:27:41]\n🔄 后端计算: 禹州大学城站 → 洪塘头站\n\n[23:27:41]\n📊 状态: 已到站\n\n[23:27:41]\n✅ 50米到站检测成功: 禹州大学城站\n\n[23:27:41]\n📋 已过站点更新: 5个站点\n\n[23:27:41]\n✅ 已过站点记录验证通过\n\n[23:27:41]\n🔄 Redis状态更新: 站点索引5\n\n[23:27:41]\n📋 已过站点: 5个\n\n[23:27:46]\n🎯 自动测试步骤 7: 前往 洪塘头站\n\n[23:27:46]\n📍 移动到 洪塘头站: (24.627789, 118.131025) 距离: 720米\n\n[23:27:46]\n✅ 位置数据发送成功: 车辆1162\n\n[23:27:46]\n📍 坐标: (24.627789, 118.131025)\n\n[23:27:46]\n🏠 地址: 福建省厦门市集美区洪塘头村洪塘大道\n\n[23:27:46]\n🔄 后端计算: 洪塘头站 → 酱文化园站\n\n[23:27:46]\n📊 状态: 已到站\n\n[23:27:46]\n✅ 50米到站检测成功: 洪塘头站\n\n[23:27:46]\n📋 已过站点更新: 6个站点\n\n[23:27:46]\n✅ 已过站点记录验证通过\n\n[23:27:46]\n🔄 Redis状态更新: 站点索引6\n\n[23:27:46]\n📋 已过站点: 6个\n\n[23:27:51]\n🎯 自动测试步骤 8: 前往 酱文化园站\n\n[23:27:51]\n📍 移动到 酱文化园站: (24.649633, 118.143592) 距离: 2741米\n\n[23:27:51]\n✅ 位置数据发送成功: 车辆1162\n\n[23:27:51]\n📍 坐标: (24.649633, 118.143592)\n\n[23:27:51]\n🏠 地址: 福建省厦门市集美区酱文化园园区内\n\n[23:27:51]\n🔄 后端计算: 酱文化园站 → 内厝站\n\n[23:27:51]\n📊 状态: 已到站\n\n[23:27:51]\n✅ 50米到站检测成功: 酱文化园站\n\n[23:27:51]\n📋 已过站点更新: 7个站点\n\n[23:27:51]\n✅ 已过站点记录验证通过\n\n[23:27:51]\n🔄 Redis状态更新: 站点索引7\n\n[23:27:51]\n📋 已过站点: 7个\n\n[23:27:52]\n💓 自动测试心跳: 车辆1162 第1轮测试进行中\n\n[23:27:56]\n🎯 自动测试步骤 9: 前往 内厝站\n\n[23:27:56]\n📍 移动到 内厝站: (24.665496, 118.273760) 距离: 13272米\n\n[23:27:56]\n✅ 位置数据发送成功: 车辆1162\n\n[23:27:56]\n📍 坐标: (24.665496, 118.273760)\n\n[23:27:56]\n🏠 地址: 福建省厦门市集美区内厝镇政府附近\n\n[23:27:56]\n🔄 后端计算: 内厝站 → 厦华科技有限公司\n\n[23:27:56]\n📊 状态: 已到站\n\n[23:27:56]\n✅ 50米到站检测成功: 内厝站\n\n[23:27:56]\n📋 已过站点更新: 8个站点\n\n[23:27:56]\n✅ 已过站点记录验证通过\n\n[23:27:56]\n🔄 Redis状态更新: 站点索引8\n\n[23:27:56]\n📋 已过站点: 8个\n\n[23:28:01]\n🎯 自动测试步骤 10: 前往 厦华科技有限公司\n\n[23:28:01]\n📍 移动到 厦华科技有限公司: (24.675460, 118.280366) 距离: 1294米\n\n[23:28:01]\n✅ 位置数据发送成功: 车辆1162\n\n[23:28:01]\n📍 坐标: (24.675460, 118.280366)\n\n[23:28:01]\n🏠 地址: 福建省厦门市集美区厦华科技有限公司园区\n\n[23:28:01]\n🔄 后端计算: 厦华科技有限公司 → 已到站\n\n[23:28:01]\n📊 状态: 已到站\n\n[23:28:01]\n✅ 50米到站检测成功: 厦华科技有限公司\n\n[23:28:01]\n📋 已过站点更新: 9个站点\n\n[23:28:01]\n✅ 已过站点记录验证通过\n\n[23:28:01]\n🔄 Redis状态更新: 站点索引9\n\n[23:28:01]\n📋 已过站点: 9个\n\n[23:28:01]\n🔄 完成第 1 个完整循环，重新从总站开始\n\n[23:28:02]\n🔌 开始测试Redis连接...\n\n[23:28:02]\n✅ Redis连接正常\n\n[23:28:02]\n📊 可以正常保存和读取车辆位置数据\n\n[23:28:02]\n🕐 服务器时间: Invalid Date\n\n[23:28:02]\n🔄 GPS API状态: 未知\n\n[23:28:06]\n🎯 自动测试步骤 11: 前往 总站(班车总站)\n\n[23:28:06]\n📍 移动到总站: (24.58, 118.1)\n\n[23:28:06]\n✅ 位置数据发送成功: 车辆1162\n\n[23:28:06]\n📍 坐标: (24.580000, 118.100000)\n\n[23:28:06]\n🏠 地址: 福建省厦门市集美区班车总站停车场\n\n[23:28:06]\n🔄 后端计算: 总站(班车总站) → 嘉庚体育馆站\n\n[23:28:06]\n📊 状态: 已到站\n\n[23:28:06]\n✅ 50米到站检测成功: 总站(班车总站)\n\n[23:28:06]\n📋 已过站点更新: 0个站点\n\n[23:28:06]\n✅ 已过站点记录验证通过\n\n[23:28:06]\n🔄 Redis状态更新: 站点索引0\n\n[23:28:06]\n📋 已过站点: 0个\n\n[23:28:09]\n✅ 位置数据发送成功: 车辆1162\n\n[23:28:09]\n📍 坐标: (24.580000, 118.100000)\n\n[23:28:09]\n🏠 地址: 福建省厦门市集美区班车总站停车场\n\n[23:28:09]\n🔄 后端计算: 总站(班车总站) → 嘉庚体育馆站\n\n[23:28:09]\n📊 状态: 已到站\n\n[23:28:09]\n✅ 50米到站检测成功: 总站(班车总站)\n\n[23:28:09]\n📋 已过站点更新: 0个站点\n\n[23:28:09]\n⚠️ 已过站点数量异常: 期望1, 实际0\n\n[23:28:09]\n🔄 Redis状态更新: 站点索引0\n\n[23:28:09]\n📋 已过站点: 0个\n\n[23:28:11]\n🎯 自动测试步骤 12: 前往 嘉庚体育馆站\n\n[23:28:11]\n📍 移动到 嘉庚体育馆站: (24.587794, 118.106892) 距离: 1112米\n\n[23:28:11]\n✅ 位置数据发送成功: 车辆1162\n\n[23:28:11]\n📍 坐标: (24.587794, 118.106892)\n\n[23:28:11]\n🏠 地址: 福建省厦门市集美区嘉庚路199号嘉庚体育馆\n\n[23:28:11]\n🔄 后端计算: 嘉庚体育馆站 → 集美旧厂区站\n\n[23:28:11]\n📊 状态: 已到站\n\n[23:28:11]\n✅ 50米到站检测成功: 嘉庚体育馆站\n\n[23:28:11]\n📋 已过站点更新: 1个站点\n\n[23:28:11]\n✅ 已过站点记录验证通过\n\n[23:28:11]\n🔄 Redis状态更新: 站点索引1\n\n[23:28:11]\n📋 已过站点: 1个\n\n[23:28:16]\n🎯 自动测试步骤 13: 前往 集美旧厂区站\n\n[23:28:16]\n📍 移动到 集美旧厂区站: (24.596247, 118.097265) 距离: 1353米\n\n[23:28:16]\n✅ 位置数据发送成功: 车辆1162\n\n[23:28:16]\n📍 坐标: (24.596247, 118.097265)\n\n[23:28:16]\n🏠 地址: 福建省厦门市集美区杏林湾路集美旧厂区\n\n[23:28:16]\n🔄 后端计算: 集美旧厂区站 → 霞梧路口站\n\n[23:28:16]\n📊 状态: 已到站\n\n[23:28:16]\n✅ 50米到站检测成功: 集美旧厂区站\n\n[23:28:16]\n📋 已过站点更新: 2个站点\n\n[23:28:16]\n✅ 已过站点记录验证通过\n\n[23:28:16]\n🔄 Redis状态更新: 站点索引2\n\n[23:28:16]\n📋 已过站点: 2个\n\n[23:28:26]\n🎯 自动测试步骤 14: 前往 霞梧路口站\n\n[23:28:26]\n📍 移动到 霞梧路口站: (24.576024, 118.097439) 距离: 2249米\n\n[23:28:26]\n💓 自动测试心跳: 车辆1162 第2轮测试进行中\n\n[23:28:32]\n🎯 自动测试步骤 15: 前往 霞梧路口站\n\n[23:28:32]\n📍 移动到 霞梧路口站: (24.575952, 118.097399) 距离: 9米\n\n[23:28:34]\n✅ 位置数据发送成功: 车辆1162\n\n[23:28:34]\n📍 坐标: (24.575952, 118.097399)\n\n[23:28:34]\n🏠 地址: 福建省厦门市集美区霞梧路与杏林北路交叉口\n\n[23:28:34]\n🔄 后端计算: 霞梧路口站 → 叶厝站\n\n[23:28:34]\n📊 状态: 已到站\n\n[23:28:34]\n✅ 50米到站检测成功: 霞梧路口站\n\n[23:28:34]\n📋 已过站点更新: 3个站点\n\n[23:28:34]\n✅ 已过站点记录验证通过\n\n[23:28:34]\n🔄 Redis状态更新: 站点索引3\n\n[23:28:34]\n📋 已过站点: 3个\n\n[23:28:34]\n✅ 位置数据发送成功: 车辆1162\n\n[23:28:34]\n📍 坐标: (24.576024, 118.097439)\n\n[23:28:34]\n🏠 地址: 福建省厦门市集美区霞梧路与杏林北路交叉口\n\n[23:28:34]\n🔄 后端计算: 霞梧路口站 → 叶厝站\n\n[23:28:34]\n📊 状态: 已到站\n\n[23:28:34]\n✅ 50米到站检测成功: 霞梧路口站\n\n[23:28:34]\n📋 已过站点更新: 3个站点\n\n[23:28:34]\n⚠️ 已过站点数量异常: 期望4, 实际3\n\n[23:28:34]\n🔄 Redis状态更新: 站点索引3\n\n[23:28:34]\n📋 已过站点: 3个\n\n[23:28:37]\n🎯 自动测试步骤 16: 前往 禹州大学城站\n\n[23:28:37]\n📍 移动到 禹州大学城站: (24.621941, 118.128008) 距离: 5977米\n\n[23:28:37]\n✅ 位置数据发送成功: 车辆1162\n\n[23:28:37]\n📍 坐标: (24.621941, 118.128008)\n\n[23:28:37]\n🏠 地址: 福建省厦门市集美区禹州大学城商业街\n\n[23:28:37]\n🔄 后端计算: 禹州大学城站 → 洪塘头站\n\n[23:28:37]\n📊 状态: 已到站\n\n[23:28:37]\n✅ 50米到站检测成功: 禹州大学城站\n\n[23:28:37]\n📋 已过站点更新: 5个站点\n\n[23:28:37]\n✅ 已过站点记录验证通过\n\n[23:28:37]\n🔄 Redis状态更新: 站点索引5\n\n[23:28:37]\n📋 已过站点: 5个\n\n[23:28:42]\n🎯 自动测试步骤 17: 前往 洪塘头站\n\n[23:28:42]\n📍 移动到 洪塘头站: (24.627794, 118.131066) 距离: 721米\n\n[23:28:42]\n✅ 位置数据发送成功: 车辆1162\n\n[23:28:42]\n📍 坐标: (24.627794, 118.131066)\n\n[23:28:42]\n🏠 地址: 福建省厦门市集美区洪塘头村洪塘大道\n\n[23:28:42]\n🔄 后端计算: 洪塘头站 → 酱文化园站\n\n[23:28:42]\n📊 状态: 已到站\n\n[23:28:42]\n✅ 50米到站检测成功: 洪塘头站\n\n[23:28:42]\n📋 已过站点更新: 6个站点\n\n[23:28:42]\n✅ 已过站点记录验证通过\n\n[23:28:42]\n🔄 Redis状态更新: 站点索引6\n\n[23:28:42]\n📋 已过站点: 6个\n\n[23:28:47]\n🎯 自动测试步骤 18: 前往 酱文化园站\n\n[23:28:47]\n📍 移动到 酱文化园站: (24.649590, 118.143638) 距离: 2736米\n\n[23:28:47]\n✅ 位置数据发送成功: 车辆1162\n\n[23:28:47]\n📍 坐标: (24.649590, 118.143638)\n\n[23:28:47]\n🏠 地址: 福建省厦门市集美区酱文化园园区内\n\n[23:28:47]\n🔄 后端计算: 酱文化园站 → 内厝站\n\n[23:28:47]\n📊 状态: 已到站\n\n[23:28:47]\n✅ 50米到站检测成功: 酱文化园站\n\n[23:28:47]\n📋 已过站点更新: 7个站点\n\n[23:28:47]\n✅ 已过站点记录验证通过\n\n[23:28:47]\n🔄 Redis状态更新: 站点索引7\n\n[23:28:47]\n📋 已过站点: 7个\n\n[23:28:52]\n🎯 自动测试步骤 19: 前往 内厝站\n\n[23:28:52]\n📍 移动到 内厝站: (24.665440, 118.273747) 距离: 13266米\n\n[23:28:52]\n✅ 位置数据发送成功: 车辆1162\n\n[23:28:52]\n📍 坐标: (24.665440, 118.273747)\n\n[23:28:52]\n🏠 地址: 福建省厦门市集美区内厝镇政府附近\n\n[23:28:52]\n🔄 后端计算: 内厝站 → 厦华科技有限公司\n\n[23:28:52]\n📊 状态: 已到站\n\n[23:28:52]\n✅ 50米到站检测成功: 内厝站\n\n[23:28:52]\n📋 已过站点更新: 8个站点\n\n[23:28:52]\n✅ 已过站点记录验证通过\n\n[23:28:52]\n🔄 Redis状态更新: 站点索引8\n\n[23:28:52]\n📋 已过站点: 8个\n\n[23:28:53]\n💓 自动测试心跳: 车辆1162 第2轮测试进行中\n\n[23:28:57]\n🎯 自动测试步骤 20: 前往 厦华科技有限公司\n\n[23:28:57]\n📍 移动到 厦华科技有限公司: (24.675543, 118.280343) 距离: 1306米\n\n[23:28:57]\n✅ 位置数据发送成功: 车辆1162\n\n[23:28:57]\n📍 坐标: (24.675543, 118.280343)\n\n[23:28:57]\n🏠 地址: 福建省厦门市集美区厦华科技有限公司园区\n\n[23:28:57]\n🔄 后端计算: 厦华科技有限公司 → 已到站\n\n[23:28:57]\n📊 状态: 已到站\n\n[23:28:57]\n✅ 50米到站检测成功: 厦华科技有限公司\n\n[23:28:57]\n📋 已过站点更新: 9个站点\n\n[23:28:57]\n✅ 已过站点记录验证通过\n\n[23:28:57]\n🔄 Redis状态更新: 站点索引9\n\n[23:28:57]\n📋 已过站点: 9个\n\n[23:28:57]\n🔄 完成第 2 个完整循环，重新从总站开始\n\n[23:29:02]\n🎯 自动测试步骤 21: 前往 总站(班车总站)\n\n[23:29:02]\n📍 移动到总站: (24.58, 118.1)\n\n[23:29:02]\n✅ 位置数据发送成功: 车辆1162\n\n[23:29:02]\n📍 坐标: (24.580000, 118.100000)\n\n[23:29:02]\n🏠 地址: 福建省厦门市集美区班车总站停车场\n\n[23:29:02]\n🔄 后端计算: 总站(班车总站) → 嘉庚体育馆站\n\n[23:29:02]\n📊 状态: 已到站\n\n[23:29:02]\n✅ 50米到站检测成功: 总站(班车总站)\n\n[23:29:02]\n📋 已过站点更新: 0个站点\n\n[23:29:02]\n✅ 已过站点记录验证通过\n\n[23:29:02]\n🔄 Redis状态更新: 站点索引0\n\n[23:29:02]\n📋 已过站点: 0个\n\n[23:29:07]\n🎯 自动测试步骤 22: 前往 嘉庚体育馆站\n\n[23:29:07]\n📍 移动到 嘉庚体育馆站: (24.587784, 118.106883) 距离: 1111米\n\n[23:29:07]\n✅ 位置数据发送成功: 车辆1162\n\n[23:29:07]\n📍 坐标: (24.587784, 118.106883)\n\n[23:29:07]\n🏠 地址: 福建省厦门市集美区嘉庚路199号嘉庚体育馆\n\n[23:29:07]\n🔄 后端计算: 嘉庚体育馆站 → 集美旧厂区站\n\n[23:29:07]\n📊 状态: 已到站\n\n[23:29:07]\n✅ 50米到站检测成功: 嘉庚体育馆站\n\n[23:29:07]\n📋 已过站点更新: 1个站点\n\n[23:29:07]\n✅ 已过站点记录验证通过\n\n[23:29:07]\n🔄 Redis状态更新: 站点索引1\n\n[23:29:07]\n📋 已过站点: 1个\n\n[23:29:12]\n🎯 自动测试步骤 23: 前往 集美旧厂区站\n\n[23:29:12]\n📍 移动到 集美旧厂区站: (24.596281, 118.097247) 距离: 1357米\n\n[23:29:12]\n✅ 位置数据发送成功: 车辆1162\n\n[23:29:12]\n📍 坐标: (24.596281, 118.097247)\n\n[23:29:12]\n🏠 地址: 福建省厦门市集美区杏林湾路集美旧厂区\n\n[23:29:12]\n🔄 后端计算: 集美旧厂区站 → 霞梧路口站\n\n[23:29:12]\n📊 状态: 已到站\n\n[23:29:12]\n✅ 50米到站检测成功: 集美旧厂区站\n\n[23:29:12]\n📋 已过站点更新: 2个站点\n\n[23:29:12]\n✅ 已过站点记录验证通过\n\n[23:29:12]\n🔄 Redis状态更新: 站点索引2\n\n[23:29:12]\n📋 已过站点: 2个\n\n[23:29:17]\n🎯 自动测试步骤 24: 前往 霞梧路口站\n\n[23:29:17]\n📍 移动到 霞梧路口站: (24.575970, 118.097445) 距离: 2259米\n\n[23:29:17]\n✅ 位置数据发送成功: 车辆1162\n\n[23:29:17]\n📍 坐标: (24.575970, 118.097445)\n\n[23:29:17]\n🏠 地址: 福建省厦门市集美区霞梧路与杏林北路交叉口\n\n[23:29:17]\n🔄 后端计算: 霞梧路口站 → 叶厝站\n\n[23:29:17]\n📊 状态: 已到站\n\n[23:29:17]\n✅ 50米到站检测成功: 霞梧路口站\n\n[23:29:17]\n📋 已过站点更新: 3个站点\n\n[23:29:17]\n✅ 已过站点记录验证通过\n\n[23:29:17]\n🔄 Redis状态更新: 站点索引3\n\n[23:29:17]\n📋 已过站点: 3个\n\n[23:29:56]\n🎯 自动测试步骤 25: 前往 叶厝站\n\n[23:29:56]\n📍 移动到 叶厝站: (24.594328, 118.106742) 距离: 2247米\n\n[23:29:56]\n💓 自动测试心跳: 车辆1162 第3轮测试进行中\n\n[23:29:56]\n✅ 位置数据发送成功: 车辆1162\n\n[23:29:56]\n📍 坐标: (24.594328, 118.106742)\n\n[23:29:56]\n🏠 地址: 福建省厦门市集美区叶厝村委会附近\n\n[23:29:56]\n🔄 后端计算: 叶厝站 → 禹州大学城站\n\n[23:29:56]\n📊 状态: 已到站\n\n[23:29:56]\n✅ 50米到站检测成功: 叶厝站\n\n[23:29:56]\n📋 已过站点更新: 4个站点\n\n[23:29:56]\n✅ 已过站点记录验证通过\n\n[23:29:56]\n🔄 Redis状态更新: 站点索引4\n\n[23:29:56]\n📋 已过站点: 4个\n\n[23:29:56]\n🎯 自动测试步骤 26: 前往 禹州大学城站\n\n[23:29:56]\n📍 移动到 禹州大学城站: (24.621917, 118.128035) 距离: 3748米\n\n[23:29:56]\n✅ 位置数据发送成功: 车辆1162\n\n[23:29:56]\n📍 坐标: (24.621917, 118.128035)\n\n[23:29:56]\n🏠 地址: 福建省厦门市集美区禹州大学城商业街\n\n[23:29:56]\n🔄 后端计算: 禹州大学城站 → 洪塘头站\n\n[23:29:56]\n📊 状态: 已到站\n\n[23:29:56]\n✅ 50米到站检测成功: 禹州大学城站\n\n[23:29:56]\n📋 已过站点更新: 5个站点\n\n[23:29:56]\n✅ 已过站点记录验证通过\n\n[23:29:57]\n🔄 Redis状态更新: 站点索引5\n\n[23:29:57]\n📋 已过站点: 5个\n\n[23:30:01]\n🎯 自动测试步骤 27: 前往 洪塘头站\n\n[23:30:01]\n📍 移动到 洪塘头站: (24.627771, 118.131055) 距离: 719米\n\n[23:30:01]\n✅ 位置数据发送成功: 车辆1162\n\n[23:30:01]\n📍 坐标: (24.627771, 118.131055)\n\n[23:30:01]\n🏠 地址: 福建省厦门市集美区洪塘头村洪塘大道\n\n[23:30:01]\n🔄 后端计算: 洪塘头站 → 酱文化园站\n\n[23:30:01]\n📊 状态: 已到站\n\n[23:30:01]\n✅ 50米到站检测成功: 洪塘头站\n\n[23:30:02]\n📋 已过站点更新: 6个站点\n\n[23:30:02]\n✅ 已过站点记录验证通过\n\n[23:30:02]\n🔄 Redis状态更新: 站点索引6\n\n[23:30:02]\n📋 已过站点: 6个\n\n[23:30:06]\n🎯 自动测试步骤 28: 前往 酱文化园站\n\n[23:30:06]\n📍 移动到 酱文化园站: (24.649573, 118.143657) 距离: 2739米\n\n[23:30:06]\n✅ 位置数据发送成功: 车辆1162\n\n[23:30:06]\n📍 坐标: (24.649573, 118.143657)\n\n[23:30:06]\n🏠 地址: 福建省厦门市集美区酱文化园园区内\n\n[23:30:06]\n🔄 后端计算: 酱文化园站 → 内厝站\n\n[23:30:06]\n📊 状态: 已到站\n\n[23:30:06]\n✅ 50米到站检测成功: 酱文化园站\n\n[23:30:06]\n📋 已过站点更新: 7个站点\n\n[23:30:07]\n✅ 已过站点记录验证通过\n\n[23:30:07]\n🔄 Redis状态更新: 站点索引7\n\n[23:30:07]\n📋 已过站点: 7个\n\n[23:30:11]\n🎯 自动测试步骤 29: 前往 内厝站\n\n[23:30:11]\n📍 移动到 内厝站: (24.665496, 118.273783) 距离: 13269米\n\n[23:30:11]\n✅ 位置数据发送成功: 车辆1162\n\n[23:30:11]\n📍 坐标: (24.665496, 118.273783)\n\n[23:30:11]\n🏠 地址: 福建省厦门市集美区内厝镇政府附近\n\n[23:30:11]\n🔄 后端计算: 内厝站 → 厦华科技有限公司\n\n[23:30:11]\n📊 状态: 已到站\n\n[23:30:11]\n✅ 50米到站检测成功: 内厝站\n\n[23:30:12]\n📋 已过站点更新: 8个站点\n\n[23:30:12]\n✅ 已过站点记录验证通过\n\n[23:30:12]\n🔄 Redis状态更新: 站点索引8\n\n[23:30:12]\n📋 已过站点: 8个\n\n[23:30:13]\n⏹️ 自动测试已停止 - 运行时长: 177秒\n\n[23:30:13]\n📊 测试统计: 成功29/29次更新, 访问29个站点, 完成2个循环\n\n[23:30:15]\n📊 === 自动测试报告 ===\n\n[23:30:15]\n🕐 测试开始时间: 2025/8/6 23:27:16\n\n[23:30:15]\n⏱️ 测试持续时间: 2分58秒\n\n[23:30:15]\n📈 更新统计: 0/0 (成功率: 0%)\n\n[23:30:15]\n🚏 访问站点: 0个\n\n[23:30:15]\n🔄 完成循环: 0次\n\n[23:30:15]\n⚡ 平均间隔: 0秒/次\n\n[23:30:15]\n📊 === 报告结束 ===\n\n[23:30:22]\n💓 系统心跳: 车辆1162 状态正常\n\n[23:30:24]\n📊 === 自动测试报告 ===\n\n[23:30:24]\n🕐 测试开始时间: 2025/8/6 23:27:16\n\n[23:30:24]\n⏱️ 测试持续时间: 3分8秒\n\n[23:30:24]\n📈 更新统计: 0/0 (成功率: 0%)\n\n[23:30:24]\n🚏 访问站点: 0个\n\n[23:30:24]\n🔄 完成循环: 0次\n\n[23:30:24]\n⚡ 平均间隔: 0秒/次\n\n[23:30:24]\n📊 === 报告结束 ===\n", "timestamp": "2025-08-06T15:30:24.795Z"}
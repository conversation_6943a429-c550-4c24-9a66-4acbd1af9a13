/* pages/login/login.wxss */
.container {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 60rpx 40rpx 40rpx;
  box-sizing: border-box;
}

.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 100rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #4ECDC4;
  border-radius: 60rpx;
}

.logo-text {
  font-size: 60rpx;
  color: white;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.login-content {
  flex: 1;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  max-width: 600rpx;
}

.intro-section {
  text-align: center;
  margin-bottom: 40rpx;
}

.intro-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20rpx;
}

.intro-desc {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.features {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30rpx;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.feature-icon {
  font-size: 40rpx;
  margin-bottom: 10rpx;
}

.feature-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

.time-notice {
  background: rgba(255, 243, 205, 0.9);
  border: 1rpx solid #FFEAA7;
  border-radius: 10rpx;
  padding: 15rpx;
  margin-bottom: 30rpx;
  text-align: center;
}

.time-notice-text {
  font-size: 24rpx;
  color: #856404;
  font-weight: 500;
}

.auth-notice {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  margin-top: 30rpx;
  text-align: center;
}

.auth-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  margin-bottom: 15rpx;
}

.privacy-link {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
}

.link-text {
  color: #4ECDC4;
  text-decoration: underline;
  margin-left: 8rpx;
}

.login-actions {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.login-btn {
  width: 100%;
  height: 88rpx;
  background: #ffffff;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  border: none;
}

.login-btn::after {
  border: none;
}

.btn-hover {
  background: #f5f5f5;
  transform: scale(0.98);
}

.btn-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.simple-login-btn {
  width: 100%;
  height: 88rpx;
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.simple-login-btn::after {
  border: none;
}



.login-footer {
  margin-bottom: 40rpx;
}

.footer-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}

// pages/login/login.js
const app = getApp()

Page({
  data: {
    canIUseGetUserProfile: wx.canIUse('getUserProfile'),
    canIUseNicknameComp: wx.canIUse('input.type.nickname'),
  },

  onLoad() {
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '厦华科技'
    })

    // 检查是否已经授权
    if (app.checkUserInfo()) {
      wx.switchTab({
        url: '/pages/index/index'
      })
    }
  },

  // 获取用户手机号 - 处理隐私协议
  getPhoneNumber(e) {
    console.log('获取手机号回调:', e.detail)

    // 检查是否是隐私协议错误
    if (e.detail.errno === 112 || e.detail.errMsg.includes('privacy agreement')) {
      this.handlePrivacyError()
      return
    }

    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      // 显示加载提示
      wx.showLoading({
        title: '授权中...'
      })

      // 先获取用户信息
      this.getUserProfile().then(() => {
        // 模拟登录成功（实际项目中需要发送code到后端解析手机号）
        this.mockLogin()
      }).catch(() => {
        wx.hideLoading()
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        })
      })
    } else {
      console.log('用户拒绝授权:', e.detail.errMsg)
      wx.showToast({
        title: '需要授权才能使用班车查询服务',
        icon: 'none',
        duration: 3000
      })
    }
  },

  // 处理隐私协议错误
  handlePrivacyError() {
    wx.showModal({
      title: '隐私保护提示',
      content: '为了给您提供更好的服务，我们需要获取您的手机号码。请先阅读并同意我们的隐私保护指引。',
      confirmText: '查看隐私指引',
      cancelText: '暂不使用',
      success: (res) => {
        if (res.confirm) {
          // 跳转到隐私政策页面
          wx.navigateTo({
            url: '/pages/privacy/privacy'
          })
        }
      }
    })
  },

  // 模拟登录（用于测试）
  mockLogin() {
    const userInfo = app.globalData.userInfo || {}

    setTimeout(() => {
      wx.hideLoading()

      // 模拟登录成功
      const mockUserInfo = {
        nickName: userInfo.nickName || '用户',
        avatarUrl: userInfo.avatarUrl || '',
        phoneNumber: '138****8888', // 模拟手机号
        openId: 'mock_openid_' + Date.now()
      }

      // 保存用户信息
      app.saveUserInfo(mockUserInfo)

      wx.showToast({
        title: '授权成功',
        icon: 'success'
      })

      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index'
        })
      }, 1500)
    }, 1000)
  },

  // 获取用户基本信息
  getUserProfile() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          app.globalData.userInfo = res.userInfo
          resolve(res)
        },
        fail: (err) => {
          wx.showToast({
            title: '需要用户信息授权',
            icon: 'none'
          })
          reject(err)
        }
      })
    })
  },

  // 使用手机号code登录
  loginWithPhoneCode(phoneCode) {
    // 获取微信登录code
    wx.login({
      success: (loginRes) => {
        if (loginRes.code) {
          this.sendLoginRequest(loginRes.code, phoneCode)
        } else {
          wx.hideLoading()
          wx.showToast({
            title: '获取登录凭证失败',
            icon: 'none'
          })
        }
      },
      fail: () => {
        wx.hideLoading()
        wx.showToast({
          title: '微信登录失败',
          icon: 'none'
        })
      }
    })
  },

  // 发送登录请求
  sendLoginRequest(loginCode, phoneCode) {
    const userInfo = app.globalData.userInfo || {}

    // 这里应该调用后端接口来解析手机号
    // 由于是演示，我们模拟一个成功的登录
    setTimeout(() => {
      wx.hideLoading()

      // 模拟登录成功
      const mockUserInfo = {
        nickName: userInfo.nickName || '用户',
        avatarUrl: userInfo.avatarUrl || '',
        phoneNumber: '138****8888', // 模拟手机号
        openId: 'mock_openid_' + Date.now()
      }

      // 保存用户信息
      app.saveUserInfo(mockUserInfo)

      wx.showToast({
        title: '授权成功',
        icon: 'success'
      })

      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index'
        })
      }, 1500)
    }, 1000)
  },

  // 直接登录 - 简化版本（保留作为备用）
  directLogin(phoneNumber) {
    // 获取微信OpenId
    wx.login({
      success: (loginRes) => {
        if (loginRes.code) {
          // 先获取OpenId
          wx.request({
            url: `${app.globalData.baseUrl}/user/login`,
            method: 'POST',
            data: {
              code: loginRes.code
            },
            success: (res) => {
              if (res.data.success) {
                const openId = res.data.data.openid

                // 直接调用简化的登录接口
                this.simpleLogin(phoneNumber, openId)
              } else {
                wx.hideLoading()
                wx.showToast({
                  title: '获取用户标识失败',
                  icon: 'none'
                })
              }
            },
            fail: () => {
              // 即使获取OpenId失败，也可以用手机号登录
              this.simpleLogin(phoneNumber, null)
            }
          })
        } else {
          // 即使微信登录失败，也可以用手机号登录
          this.simpleLogin(phoneNumber, null)
        }
      },
      fail: () => {
        // 即使微信登录失败，也可以用手机号登录
        this.simpleLogin(phoneNumber, null)
      }
    })
  },

  // 简化的登录方法
  simpleLogin(phoneNumber, openId) {
    const userInfo = app.globalData.userInfo || {}

    wx.request({
      url: `${app.globalData.baseUrl}/auth/login`,
      method: 'POST',
      data: {
        phoneNumber: phoneNumber,
        openId: openId,
        nickName: userInfo.nickName,
        avatarUrl: userInfo.avatarUrl
      },
      success: (res) => {
        wx.hideLoading()

        if (res.data.success) {
          // 保存用户信息到全局
          app.globalData.userInfo = res.data.userInfo
          app.globalData.isLoggedIn = true

          wx.showToast({
            title: '授权成功',
            icon: 'success'
          })

          setTimeout(() => {
            wx.switchTab({
              url: '/pages/index/index'
            })
          }, 1500)
        } else {
          wx.showToast({
            title: res.data.message || '登录失败',
            icon: 'none'
          })
        }
      },
      fail: () => {
        wx.hideLoading()
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        })
      }
    })
  },

  // 简化登录（不需要手机号）
  simpleLogin() {
    wx.showLoading({
      title: '登录中...'
    })

    // 获取用户基本信息
    this.getUserProfile().then(() => {
      this.mockLogin()
    }).catch(() => {
      // 即使不获取用户信息也可以登录
      this.mockLoginWithoutProfile()
    })
  },

  // 不获取用户信息的模拟登录
  mockLoginWithoutProfile() {
    setTimeout(() => {
      wx.hideLoading()

      // 模拟登录成功
      const mockUserInfo = {
        nickName: '游客用户',
        avatarUrl: '',
        phoneNumber: '', // 不获取手机号
        openId: 'guest_' + Date.now()
      }

      // 保存用户信息
      app.saveUserInfo(mockUserInfo)

      wx.showToast({
        title: '登录成功',
        icon: 'success'
      })

      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index'
        })
      }, 1500)
    }, 1000)
  },

  // 跳转到隐私政策页面
  goToPrivacy() {
    wx.navigateTo({
      url: '/pages/privacy/privacy'
    })
  }
})

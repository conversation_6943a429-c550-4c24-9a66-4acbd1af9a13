using System.Collections.Concurrent;

namespace WeChatBus.Services;

/// <summary>
/// 内存事件总线
/// 用于在进程内分发Redis事件到长轮询请求
/// </summary>
public class EventBus
{
    /// <summary>
    /// 存储频道订阅者
    /// </summary>
    private readonly ConcurrentDictionary<string, ConcurrentDictionary<Guid, Action<string>>> _listeners = new();
    private readonly ILogger<EventBus> _logger;

    public EventBus(ILogger<EventBus> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 订阅频道事件
    /// </summary>
    /// <param name="channel">频道名称</param>
    /// <param name="callback">回调函数</param>
    public Guid Subscribe(string channel, Action<string> callback)
    {
        var dict = _listeners.GetOrAdd(channel, _ => new ConcurrentDictionary<Guid, Action<string>>());
        var id = Guid.NewGuid();
        dict[id] = callback;
        _logger.LogDebug("订阅频道: {Channel}, 当前订阅者数量: {Count}", channel, dict.Count);
        return id;
    }

    /// <summary>
    /// 取消订阅频道事件
    /// </summary>
    /// <param name="channel">频道名称</param>
    /// <param name="callback">回调函数</param>
    public void Unsubscribe(string channel, Guid id)
    {
        if (_listeners.TryGetValue(channel, out var dict))
        {
            dict.TryRemove(id, out _);
            _logger.LogDebug("取消订阅频道: {Channel}, 剩余订阅者数量: {Count}", channel, dict.Count);
        }
    }

    /// <summary>
    /// 发布事件到频道
    /// </summary>
    /// <param name="channel">频道名称</param>
    /// <param name="message">消息内容</param>
    public void Publish(string channel, string message)
    {
        if (_listeners.TryGetValue(channel, out var dict))
        {
            var callbackCount = 0;
            foreach (var callback in dict.Values)
            {
                try
                {
                    callback(message);
                    callbackCount++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "执行事件回调时发生异常, 频道: {Channel}", channel);
                }
            }
            _logger.LogDebug("发布事件到频道: {Channel}, 通知了 {Count} 个订阅者", channel, callbackCount);
        }
        else
        {
            _logger.LogDebug("发布事件到频道: {Channel}, 但没有订阅者", channel);
        }
    }

    /// <summary>
    /// 获取频道的订阅者数量
    /// </summary>
    /// <param name="channel">频道名称</param>
    /// <returns>订阅者数量</returns>
    public int GetSubscriberCount(string channel)
    {
        return _listeners.TryGetValue(channel, out var bag) ? bag.Count : 0;
    }

    /// <summary>
    /// 获取所有频道信息
    /// </summary>
    /// <returns>频道信息字典</returns>
    public Dictionary<string, int> GetAllChannels()
    {
        return _listeners.ToDictionary(
            kvp => kvp.Key,
            kvp => kvp.Value.Count
        );
    }

    /// <summary>
    /// 清理空的频道
    /// </summary>
    public void CleanupEmptyChannels()
    {
        var emptyChannels = _listeners
            .Where(kvp => kvp.Value.IsEmpty)
            .Select(kvp => kvp.Key)
            .ToList();

        foreach (var channel in emptyChannels)
        {
            _listeners.TryRemove(channel, out _);
            _logger.LogDebug("清理空频道: {Channel}", channel);
        }

        if (emptyChannels.Any())
        {
            _logger.LogInformation("清理了 {Count} 个空频道", emptyChannels.Count);
        }
    }
}

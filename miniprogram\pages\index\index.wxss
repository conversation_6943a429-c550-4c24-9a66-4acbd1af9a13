/* pages/index/index.wxss - 现代化设计版本 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 现代化头部 */
.header {
  background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
  padding: 50rpx 30rpx 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(78, 205, 196, 0.3);
  position: relative;
  overflow: hidden;
}

.header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: headerShine 3s infinite;
}

@keyframes headerShine {
  0%, 100% { transform: rotate(0deg); }
  50% { transform: rotate(180deg); }
}

.title {
  font-size: 38rpx;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
  position: relative;
  z-index: 1;
}

.auth-text {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.auth-btn {
  font-size: 22rpx;
  color: #4CAF50;
  padding: 5rpx 10rpx;
  border: 1rpx solid #4CAF50;
  border-radius: 10rpx;
}

/* 时间状态 - 现代化设计 */
.time-status {
  margin: 30rpx;
  padding: 35rpx;
  border-radius: 25rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.15);
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
}

.time-status.active {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(76, 175, 80, 0.4);
}

.time-status.inactive {
  background: linear-gradient(135deg, #FF5722 0%, #e64a19 100%);
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(255, 87, 34, 0.4);
}

.time-info {
  display: flex;
  flex-direction: column;
}

.time-text {
  font-size: 34rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
}

.status-text {
  font-size: 26rpx;
  opacity: 0.9;
  font-weight: 500;
}

.status-indicator {
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  animation: pulse 2s infinite;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.2);
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.9;
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.2);
  }
  50% {
    transform: scale(1.3);
    opacity: 1;
    box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.3);
  }
  100% {
    transform: scale(1);
    opacity: 0.9;
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.2);
  }
}

/* 内容区域 */
.content {
  padding: 30rpx 30rpx 30rpx;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 120rpx 0;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 25rpx;
  margin: 20rpx 0;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}

.loading-text {
  font-size: 30rpx;
  color: #4ECDC4;
  font-weight: 500;
  animation: loadingPulse 1.5s infinite;
}

@keyframes loadingPulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* 时间提示 */
.time-notice {
  background: #fff;
  border-radius: 15rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.notice-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.notice-text {
  font-size: 32rpx;
  color: #666;
}

/* 班车列表 */
.bus-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.bus-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
  backdrop-filter: blur(10rpx);
  position: relative;
  overflow: hidden;
}

.bus-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(255,255,255,0.1) 100%);
  pointer-events: none;
}

.bus-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.15);
}

/* 离线状态样式 */
.bus-item.offline {
  opacity: 0.8; /* 降低透明度但不会太灰 */
  background: rgba(200, 200, 200, 0.1);
  /* 移除 pointer-events: none，让它可以点击 */
}

.bus-item.offline:active {
  transform: scale(0.96); /* 点击时有反馈 */
  background: rgba(180, 180, 180, 0.2);
}

.bus-icon.offline {
  background: #999999 !important;
  opacity: 0.8;
}

.bus-name.offline,
.bus-car.offline {
  color: #666666 !important;
}

.status-text.offline {
  color: #999999 !important;
}

.offline-info {
  margin-top: 10rpx;
}

.offline-text {
  font-size: 24rpx;
  color: #999999;
  font-weight: 500;
}

/* 灰色不可点击状态 - 优先级最高 */
.bus-item.disabled {
  opacity: 0.6;
  background: rgba(248, 248, 248, 0.9);
  pointer-events: none !important; /* 确保时间范围外时完全不可点击 */
}

.bus-item.disabled .bus-name,
.bus-item.disabled .bus-car {
  color: #999 !important;
}

.bus-item.disabled .status-text {
  color: #999 !important;
}

/* 班车图标 */
.bus-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 25rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.2);
  position: relative;
  z-index: 1;
}

.icon-text {
  font-size: 36rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0,0,0,0.3));
}

/* 班车内容区域 */
.bus-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.bus-info {
  display: flex;
  flex-direction: column;
}

.bus-name {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.1);
}

.bus-car {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

/* 实时位置信息 */
.bus-location {
  margin-top: 8rpx;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.location-text {
  font-size: 24rpx;
  color: #4ECDC4;
  margin-right: 4rpx;
}

.location-station {
  font-size: 24rpx;
  color: #4ECDC4;
  font-weight: bold;
}

/* 下一站信息样式 */
.next-station-info {
  display: flex;
  align-items: center;
  margin-top: 4rpx;
}

.next-text {
  font-size: 24rpx;
  color: #FF6B6B;
  margin-right: 4rpx;
}

.next-station {
  font-size: 24rpx;
  color: #FF6B6B;
  font-weight: bold;
}

/* 状态区域 */
.bus-status {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.status-dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.2);
}

.status-dot.online {
  background: #4CAF50;
}

.status-dot.offline {
  background: #999;
}

.status-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.bus-info {
  flex: 1;
}

.bus-name {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.bus-car {
  font-size: 26rpx;
  color: #666;
}

.bus-status {
  display: flex;
  align-items: center;
}

.bus-status .status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 10rpx;
}

.bus-status .status-dot.active {
  background: #4CAF50;
}

.bus-status .status-dot.inactive {
  background: #999;
}

.status-text {
  font-size: 26rpx;
  color: #4CAF50;
}

/* 授权提示 */
.auth-notice {
  background: #fff;
  border-radius: 15rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.notice-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.notice-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 30rpx;
  line-height: 1.5;
}

.auth-button {
  background: #4CAF50;
  color: #fff;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.auth-button::after {
  border: none;
}

/* 授权弹窗 */
.auth-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600rpx;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 40rpx 30rpx;
  text-align: center;
}

.auth-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.auth-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  border: none;
  border-radius: 0;
}

.cancel-btn {
  background: #f8f9fa;
  color: #666;
  border-right: 1rpx solid #f0f0f0;
}

.confirm-btn {
  background: #4CAF50;
  color: #fff;
}

.cancel-btn::after,
.confirm-btn::after {
  border: none;
}

/* 错误状态样式 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  animation: shake 1s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-10rpx); }
  75% { transform: translateX(10rpx); }
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.error-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6b6b;
  margin-bottom: 20rpx;
}

.error-desc {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.retry-btn {
  background: #4ECDC4;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.retry-btn::after {
  border: none;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 100rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-20rpx); }
}

.empty-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #666;
  margin-bottom: 15rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
}

/* 加载状态优化 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.loading-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
  animation: bounce 1.5s ease-in-out infinite;
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-20rpx); }
}

.loading-text {
  font-size: 28rpx;
  color: #4ECDC4;
  font-weight: 500;
}

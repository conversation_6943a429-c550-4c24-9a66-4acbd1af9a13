/* pages/route/route.wxss */
.container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 30rpx;
}

/* 错误提示容器 */
.error-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.error-content {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin: 40rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  max-width: 600rpx;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  display: block;
}

.error-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}

.error-message {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 40rpx;
  display: block;
}

.error-actions {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

.retry-btn, .refresh-btn-alt {
  flex: 1;
  max-width: 200rpx;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
}

.retry-btn {
  background: #007aff;
  color: white;
}

.refresh-btn-alt {
  background: #f0f0f0;
  color: #333333;
}

.bus-info-card {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.bus-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.bus-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.icon-text {
  font-size: 36rpx;
  color: white;
}

.bus-details {
  flex: 1;
}

.bus-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.bus-number {
  font-size: 26rpx;
  color: #666666;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.connection-status {
  display: flex;
  align-items: center;
}

.status-text {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  background: #f0f0f0;
  color: #666;
}

.status-text.connected {
  background: #e8f5e8;
  color: #4CAF50;
}

.status-text.error {
  background: #ffeaea;
  color: #f44336;
}

.status-text.connecting {
  background: #fff3e0;
  color: #ff9800;
}

.auto-refresh-btn {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: #f0f0f0;
  border: 2rpx solid #e0e0e0;
  transition: all 0.3s ease;
}

.auto-refresh-btn.active {
  background: #4ECDC4;
  border-color: #4ECDC4;
}

.refresh-status {
  font-size: 22rpx;
  color: #666666;
}

.auto-refresh-btn.active .refresh-status {
  color: #ffffff;
}

.refresh-btn {
  padding: 10rpx;
}

.refresh-icon {
  font-size: 32rpx;
  color: #666;
}

.bus-status {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  font-size: 26rpx;
  font-weight: bold;
  color: #666666;
}

.status-value {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
}

.status-value-highlight {
  font-size: 26rpx;
  color: #4ECDC4;
  font-weight: 600;
}

/* 详细位置信息样式 */
.detail-location {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 15rpx;
  margin-top: 15rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.detail-label {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
  margin-bottom: 8rpx;
}

.detail-value-full {
  font-size: 24rpx;
  color: #666;
  font-weight: normal;
  word-break: break-all;
  white-space: normal;
  line-height: 1.5;
  text-align: left;
  width: 100%;
}

.detail-value {
  font-size: 24rpx;
  color: #666;
  font-weight: normal;
}

.detail-value-inline {
  font-size: 24rpx;
  color: #666;
  font-weight: normal;
  word-break: keep-all;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.distance-info {
  color: #4ECDC4;
  font-size: 22rpx;
}

.status-at-station {
  color: #28a745 !important;
  font-weight: bold;
}

.status-moving {
  color: #ffc107 !important;
}

.loading {
  text-align: center;
  padding: 40rpx 0;
}

.loading-text {
  font-size: 26rpx;
  color: #999999;
}

.route-section {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.section-header {
  margin-bottom: 30rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.section-desc {
  font-size: 24rpx;
  color: #999999;
}

/* 预估时间卡片样式 - 按照图片样式 */
.estimated-card {
  position: relative;
  margin: 20rpx 30rpx;
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%);
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #4ECDC4;
}

.estimated-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.estimated-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff6b35;
  text-align: center;
}

.estimated-detail {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  text-align: center;
}

.estimated-detail.calculating {
  color: #666666;
  font-style: italic;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.estimated-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.estimated-station {
  font-size: 48rpx;
  font-weight: bold;
  color: #ff6b35;
  margin-bottom: 10rpx;
}

.estimated-detail {
  font-size: 28rpx;
  color: #666666;
  font-weight: 500;
}

/* 水平站点列表样式 - 参考掌上公交设计 */
.stations-container {
  width: 100%;
  white-space: nowrap;
}

.stations-wrapper {
  display: inline-flex;
  align-items: flex-start;
  position: relative;
  padding: 30rpx 40rpx 20rpx 20rpx;
  min-width: 100%;
}

.connection-line {
  position: absolute;
  top: 65rpx;
  left: 50rpx;
  right: 50rpx;
  height: 4rpx;
  background: linear-gradient(to right, #4ECDC4 0%, #4ECDC4 var(--passed-percentage, 0%), #e0e0e0 var(--passed-percentage, 0%), #e0e0e0 100%);
  border-radius: 2rpx;
  z-index: 1;
}

.station-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 70rpx;
  position: relative;
  z-index: 2;
  min-width: 100rpx;
  transition: all 0.3s ease;
}

.station-item:last-child {
  margin-right: 40rpx;
}

.station-item.clickable {
  cursor: pointer;
}

.station-item.clickable:active {
  transform: scale(0.95);
}

.station-item.clickable:active .station-dot {
  background: #fff3cd;
  border-color: #ffc107;
  box-shadow: 0 0 0 4rpx rgba(255, 193, 7, 0.3);
}

.station-dot {
  width: 50rpx;
  height: 50rpx;
  border-radius: 25rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  position: relative;
  transition: all 0.3s ease;
}

.station-item.waiting .station-dot {
  background: #f5f5f5;
  border: 3rpx solid #e0e0e0;
}

.station-item.current .station-dot {
  background: #4ECDC4;
  border: 3rpx solid #ffffff;
  box-shadow: 0 0 0 3rpx #4ECDC4;
  animation: pulse 2s infinite;
}

.station-item.approaching .station-dot {
  background: #ffc107;
  border: 3rpx solid #ffffff;
  box-shadow: 0 0 0 3rpx #ffc107;
}

.station-item.passed .station-dot {
  background: #4ECDC4;
  border: 3rpx solid #ffffff;
}

/* 圆圈中心的序号样式 */
.station-number-center {
  font-size: 20rpx;
  font-weight: bold;
  color: #666666;
  z-index: 2;
}

.station-item.waiting .station-number-center {
  color: #999999;
}

.station-item.current .station-number-center {
  color: #ffffff;
}

.station-item.passed .station-number-center {
  color: #ffffff;
}

/* 目标站点高亮样式 */
.station-item.target-station .station-dot {
  border: 4rpx solid #ff6b35 !important;
  box-shadow: 0 0 0 4rpx rgba(255, 107, 53, 0.3) !important;
  animation: targetPulse 2s infinite;
}

.station-item.target-station .station-number-center {
  color: #ff6b35 !important;
  font-weight: bold;
}

@keyframes targetPulse {
  0% {
    box-shadow: 0 0 0 4rpx rgba(255, 107, 53, 0.3);
  }
  50% {
    box-shadow: 0 0 0 8rpx rgba(255, 107, 53, 0.1);
  }
  100% {
    box-shadow: 0 0 0 4rpx rgba(255, 107, 53, 0.3);
  }
}

/* 动画小班车样式 - 增强可见性 */
.moving-bus {
  position: absolute;
  top: 10rpx;
  right: -60rpx;
  z-index: 3;
  width: 90rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.3) 0%, rgba(78, 205, 196, 0.2) 100%);
  border-radius: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(78, 205, 196, 0.6);
  border: 2rpx solid rgba(78, 205, 196, 0.5);
  /* 添加调试背景以确保可见 */
  background-color: rgba(78, 205, 196, 0.15);
}

.bus-icon-moving {
  font-size: 36rpx;
  animation: busMoving 2.5s infinite ease-in-out;
  filter: drop-shadow(0 2rpx 4rpx rgba(78, 205, 196, 0.5));
  /* 增强动画可见性 */
  transform-origin: center;
  will-change: transform, opacity;
}

@keyframes busMoving {
  0% {
    transform: translateX(-15rpx) scale(0.85) rotate(-2deg);
    opacity: 0.7;
  }
  25% {
    transform: translateX(-5rpx) scale(0.95) rotate(0deg);
    opacity: 0.9;
  }
  50% {
    transform: translateX(5rpx) scale(1.1) rotate(1deg);
    opacity: 1;
  }
  75% {
    transform: translateX(15rpx) scale(1.05) rotate(0deg);
    opacity: 0.9;
  }
  100% {
    transform: translateX(-15rpx) scale(0.85) rotate(-2deg);
    opacity: 0.7;
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 3rpx #4ECDC4;
  }
  50% {
    box-shadow: 0 0 0 6rpx rgba(78, 205, 196, 0.5);
  }
  100% {
    box-shadow: 0 0 0 3rpx #4ECDC4;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-8rpx);
  }
  60% {
    transform: translateY(-4rpx);
  }
}

/* 移除dot-inner相关样式，序号直接显示在圆圈中心 */

.bus-marker {
  position: absolute;
  top: -12rpx;
  right: -12rpx;
  width: 30rpx;
  height: 30rpx;
  background: #ff6b35;
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #ffffff;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
  animation: bounce 1s infinite;
}

.bus-icon-small {
  font-size: 16rpx;
  color: #ffffff;
}

.station-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
}

.station-name {
  font-size: 24rpx;
  font-weight: 500;
  color: #333333;
  line-height: 1.2;
  word-break: break-all;
  max-width: 100rpx;
}

.station-item.current .station-name {
  color: #4ECDC4;
  font-weight: bold;
}

.station-item.passed .station-name {
  color: #4ECDC4;
}

.tips-section {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.tip-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  font-size: 28rpx;
  margin-right: 15rpx;
  display: inline-block;
  width: 32rpx;
  text-align: center;
}

.tip-text {
  font-size: 24rpx;
  color: #666666;
}

/* 加载状态样式 */
.calculating-time {
  display: block;
  font-size: 24rpx;
  color: #4ECDC4;
  font-weight: 500;
  margin-top: 5rpx;
  animation: blink 1.5s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.5; }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .station-name-vertical {
    font-size: 28rpx;
  }

  .station-status-vertical {
    font-size: 22rpx;
  }

  .estimated-time {
    font-size: 24rpx;
  }

  .tap-hint-text {
    font-size: 20rpx;
  }
}

/* 无障碍访问优化 */
.station-item-vertical.clickable {
  outline: none;
}

.station-item-vertical.clickable:focus {
  background: rgba(255, 165, 0, 0.1);
  border-radius: 15rpx;
  padding: 10rpx;
  margin: -10rpx;
  margin-bottom: 30rpx;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .station-name-vertical {
    color: #ffffff;
  }

  .station-status-vertical {
    color: #cccccc;
  }

  .connection-line-vertical {
    background: linear-gradient(to bottom, #4ECDC4 0%, #4ECDC4 var(--passed-percentage, 0%), #555555 var(--passed-percentage, 0%), #555555 100%);
  }

  .station-item-vertical.waiting .station-dot-vertical {
    background: #333333;
    border-color: #555555;
  }
}

/* 详情页面错误状态样式 */
.route-error-state {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-top: 1rpx solid #f0f0f0;
  margin-top: 20rpx;
}

.route-error-icon {
  font-size: 60rpx;
  margin-right: 20rpx;
}

.route-error-content {
  flex: 1;
}

.route-error-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #ff6b6b;
  margin-bottom: 10rpx;
}

.route-error-desc {
  display: block;
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
  margin-bottom: 20rpx;
}

.route-retry-btn {
  background: #4ECDC4;
  color: #fff;
  border: none;
  border-radius: 30rpx;
  padding: 15rpx 40rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.route-retry-btn::after {
  border: none;
}

/* 错误重试样式 */
.error-section {
  margin: 30rpx;
  padding: 30rpx;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border-left: 8rpx solid #f44336;
}

.error-message {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.error-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.error-text {
  font-size: 28rpx;
  color: #f44336;
  flex: 1;
}

.retry-btn {
  width: 100%;
  height: 80rpx;
  background: #4ECDC4;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.retry-btn::after {
  border: none;
}

/* 到站标记样式 */
.arrived-marker {
  position: absolute;
  top: -15rpx;
  left: -15rpx;
  width: 36rpx;
  height: 36rpx;
  background: #4CAF50;
  border: 3rpx solid #ffffff;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  animation: pulse 1.5s ease-in-out infinite alternate;
}

.arrived-icon {
  font-size: 18rpx;
  color: white;
}

/* 到站标记文本样式 */
.arrived-badge {
  margin-left: 20rpx;
  padding: 8rpx 16rpx;
  background: #4CAF50;
  color: white;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: bold;
  animation: pulse 1.5s ease-in-out infinite alternate;
}

/* 到站脉冲动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.1);
  }
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>长轮询测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.connected { background-color: #d4edda; color: #155724; }
        .status.connecting { background-color: #fff3cd; color: #856404; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .data-display {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .log {
            background-color: #f1f3f4;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>长轮询测试页面</h1>
        
        <div class="status" id="status">未连接</div>
        
        <div>
            <button onclick="startPolling()" id="startBtn">开始长轮询</button>
            <button onclick="stopPolling()" id="stopBtn" disabled>停止长轮询</button>
            <button onclick="triggerUpdate()">手动触发更新</button>
            <button onclick="clearLogs()">清除日志</button>
        </div>
        
        <h3>最新数据:</h3>
        <div class="data-display" id="dataDisplay">等待数据...</div>
        
        <h3>日志:</h3>
        <div class="log" id="logDisplay"></div>
    </div>

    <script>
        let isPolling = false;
        let pollTimeout = null;
        
        function log(message) {
            const logDiv = document.getElementById('logDisplay');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(status, className) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = status;
            statusDiv.className = `status ${className}`;
        }
        
        function updateData(data) {
            const dataDiv = document.getElementById('dataDisplay');
            dataDiv.textContent = JSON.stringify(data, null, 2);
        }
        
        async function poll() {
            if (!isPolling) return;
            
            try {
                updateStatus('连接中...', 'connecting');
                log('发起长轮询请求...');
                
                const response = await fetch('https://localhost:54759/api/longpolling/poll?timeout=10', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.status === 200) {
                    const data = await response.json();
                    log(`收到数据: ${data.data?.locations?.length || 0} 个位置`);
                    updateStatus('已连接', 'connected');
                    updateData(data);
                } else if (response.status === 204) {
                    log('长轮询超时，没有新数据');
                    updateStatus('已连接', 'connected');
                } else {
                    log(`响应错误: ${response.status}`);
                    updateStatus('连接错误', 'error');
                }
                
                // 立即发起下一次轮询
                if (isPolling) {
                    setTimeout(poll, 100);
                }
                
            } catch (error) {
                log(`请求失败: ${error.message}`);
                updateStatus('连接失败', 'error');
                
                // 重试
                if (isPolling) {
                    setTimeout(poll, 3000);
                }
            }
        }
        
        function startPolling() {
            if (isPolling) return;
            
            isPolling = true;
            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            
            log('开始长轮询');
            poll();
        }
        
        function stopPolling() {
            isPolling = false;
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            
            updateStatus('未连接', '');
            log('停止长轮询');
        }
        
        async function triggerUpdate() {
            try {
                log('手动触发更新...');
                const response = await fetch('/api/vehicle/poll/trigger', {
                    method: 'POST'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`触发成功: ${data.message}`);
                } else {
                    log(`触发失败: ${response.status}`);
                }
            } catch (error) {
                log(`触发失败: ${error.message}`);
            }
        }
        
        function clearLogs() {
            document.getElementById('logDisplay').innerHTML = '';
            document.getElementById('dataDisplay').textContent = '等待数据...';
        }
        
        // 页面加载完成后的初始化
        window.onload = function() {
            log('页面加载完成');
        };
        
        // 页面关闭时停止轮询
        window.onbeforeunload = function() {
            stopPolling();
        };
    </script>
</body>
</html>

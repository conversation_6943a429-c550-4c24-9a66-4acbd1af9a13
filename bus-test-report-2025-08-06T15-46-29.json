{"report": {"testStartTime": "2025/8/6 23:44:40", "testDuration": "1分49秒", "totalUpdates": 21, "successfulUpdates": 21, "failedUpdates": 0, "successRate": "100%", "stationsVisited": 21, "cyclesCompleted": 2, "averageUpdateInterval": 5, "testStatus": "已完成", "dataIntegrity": "正常", "debugInfo": {"autoTestStatsSnapshot": "{\"totalUpdates\":0,\"successfulUpdates\":0,\"failedUpdates\":0,\"stationsVisited\":0,\"cyclesCompleted\":0}", "globalStatsSnapshot": "{\"totalUpdates\":21,\"successfulUpdates\":21,\"stationsVisited\":21,\"failedUpdates\":0,\"cyclesCompleted\":2}", "testRunning": false, "startTime": 1754495080182}}, "logs": "[启动]\n班车位置模拟器已启动\n\n[23:44:33]\n⌨️ 快捷键: Ctrl+S发送位置, Ctrl+T自动测试, Ctrl+L长轮询, Ctrl+R测试Redis\n\n[23:44:33]\n页面初始化完成 - 所有状态由后端计算\n\n[23:44:40]\n🚀 开始智能自动测试 - 站点循环模式\n\n[23:44:40]\n📋 测试计划: 按顺序访问所有站点，触发50米到站检测，验证后端业务逻辑\n\n[23:44:40]\n🎯 自动测试步骤 1: 前往 总站(班车总站) (索引0)\n\n[23:44:40]\n📍 移动到总站: (24.58, 118.1)\n\n[23:44:40]\n✅ 位置数据发送成功: 车辆1162\n\n[23:44:40]\n📍 坐标: (24.580000, 118.100000)\n\n[23:44:40]\n🏠 地址: 福建省厦门市集美区班车总站停车场\n\n[23:44:40]\n🔄 后端计算: 总站(班车总站) → 嘉庚体育馆站\n\n[23:44:40]\n📊 状态: 已到站\n\n[23:44:40]\n✅ 50米到站检测成功: 总站(班车总站)\n\n[23:44:40]\n📋 已过站点更新: 0个站点\n\n[23:44:40]\n✅ 已过站点记录验证通过\n\n[23:44:40]\n🔄 Redis状态更新: 站点索引0\n\n[23:44:40]\n📋 已过站点: 0个\n\n[23:44:40]\n📊 统计更新: 总计1, 成功1, 失败0\n\n[23:44:45]\n🎯 自动测试步骤 2: 前往 嘉庚体育馆站 (索引1)\n\n[23:44:45]\n📍 移动到 嘉庚体育馆站: (24.587738, 118.106886) 距离: 1107米\n\n[23:44:45]\n✅ 位置数据发送成功: 车辆1162\n\n[23:44:45]\n📍 坐标: (24.587738, 118.106886)\n\n[23:44:45]\n🏠 地址: 福建省厦门市集美区嘉庚路199号嘉庚体育馆\n\n[23:44:45]\n🔄 后端计算: 嘉庚体育馆站 → 集美旧厂区站\n\n[23:44:45]\n📊 状态: 已到站\n\n[23:44:45]\n✅ 50米到站检测成功: 嘉庚体育馆站\n\n[23:44:45]\n📋 已过站点更新: 1个站点\n\n[23:44:45]\n✅ 已过站点记录验证通过\n\n[23:44:45]\n🔄 Redis状态更新: 站点索引1\n\n[23:44:45]\n📋 已过站点: 1个\n\n[23:44:45]\n📊 统计更新: 总计2, 成功2, 失败0\n\n[23:44:50]\n🎯 自动测试步骤 3: 前往 集美旧厂区站 (索引2)\n\n[23:44:50]\n📍 移动到 集美旧厂区站: (24.596214, 118.097230) 距离: 1357米\n\n[23:44:50]\n✅ 位置数据发送成功: 车辆1162\n\n[23:44:50]\n📍 坐标: (24.596214, 118.097230)\n\n[23:44:50]\n🏠 地址: 福建省厦门市集美区杏林湾路集美旧厂区\n\n[23:44:50]\n🔄 后端计算: 集美旧厂区站 → 霞梧路口站\n\n[23:44:50]\n📊 状态: 已到站\n\n[23:44:50]\n✅ 50米到站检测成功: 集美旧厂区站\n\n[23:44:50]\n📋 已过站点更新: 2个站点\n\n[23:44:50]\n✅ 已过站点记录验证通过\n\n[23:44:50]\n🔄 Redis状态更新: 站点索引2\n\n[23:44:50]\n📋 已过站点: 2个\n\n[23:44:50]\n📊 统计更新: 总计3, 成功3, 失败0\n\n[23:44:55]\n🎯 自动测试步骤 4: 前往 霞梧路口站 (索引3)\n\n[23:44:55]\n📍 移动到 霞梧路口站: (24.575958, 118.097446) 距离: 2252米\n\n[23:44:55]\n✅ 位置数据发送成功: 车辆1162\n\n[23:44:55]\n📍 坐标: (24.575958, 118.097446)\n\n[23:44:55]\n🏠 地址: 福建省厦门市集美区霞梧路与杏林北路交叉口\n\n[23:44:55]\n🔄 后端计算: 霞梧路口站 → 叶厝站\n\n[23:44:55]\n📊 状态: 已到站\n\n[23:44:55]\n✅ 50米到站检测成功: 霞梧路口站\n\n[23:44:55]\n📋 已过站点更新: 3个站点\n\n[23:44:55]\n✅ 已过站点记录验证通过\n\n[23:44:55]\n🔄 Redis状态更新: 站点索引3\n\n[23:44:55]\n📋 已过站点: 3个\n\n[23:44:55]\n📊 统计更新: 总计4, 成功4, 失败0\n\n[23:45:00]\n🎯 自动测试步骤 5: 前往 叶厝站 (索引4)\n\n[23:45:00]\n📍 移动到 叶厝站: (24.594325, 118.106699) 距离: 2246米\n\n[23:45:00]\n✅ 位置数据发送成功: 车辆1162\n\n[23:45:00]\n📍 坐标: (24.594325, 118.106699)\n\n[23:45:00]\n🏠 地址: 福建省厦门市集美区叶厝村委会附近\n\n[23:45:00]\n🔄 后端计算: 叶厝站 → 禹州大学城站\n\n[23:45:00]\n📊 状态: 已到站\n\n[23:45:00]\n✅ 50米到站检测成功: 叶厝站\n\n[23:45:00]\n📋 已过站点更新: 4个站点\n\n[23:45:00]\n✅ 已过站点记录验证通过\n\n[23:45:00]\n🔄 Redis状态更新: 站点索引4\n\n[23:45:00]\n📋 已过站点: 4个\n\n[23:45:00]\n📊 统计更新: 总计5, 成功5, 失败0\n\n[23:45:03]\n💓 自动测试心跳: 车辆1162 第1轮测试进行中\n\n[23:45:05]\n🎯 自动测试步骤 6: 前往 禹州大学城站 (索引5)\n\n[23:45:05]\n📍 移动到 禹州大学城站: (24.621954, 118.127948) 距离: 3749米\n\n[23:45:05]\n✅ 位置数据发送成功: 车辆1162\n\n[23:45:05]\n📍 坐标: (24.621954, 118.127948)\n\n[23:45:05]\n🏠 地址: 福建省厦门市集美区禹州大学城商业街\n\n[23:45:05]\n🔄 后端计算: 禹州大学城站 → 洪塘头站\n\n[23:45:05]\n📊 状态: 已到站\n\n[23:45:05]\n✅ 50米到站检测成功: 禹州大学城站\n\n[23:45:05]\n📋 已过站点更新: 5个站点\n\n[23:45:05]\n✅ 已过站点记录验证通过\n\n[23:45:05]\n🔄 Redis状态更新: 站点索引5\n\n[23:45:05]\n📋 已过站点: 5个\n\n[23:45:05]\n📊 统计更新: 总计6, 成功6, 失败0\n\n[23:45:10]\n🎯 自动测试步骤 7: 前往 洪塘头站 (索引6)\n\n[23:45:10]\n📍 移动到 洪塘头站: (24.627797, 118.131012) 距离: 720米\n\n[23:45:10]\n✅ 位置数据发送成功: 车辆1162\n\n[23:45:10]\n📍 坐标: (24.627797, 118.131012)\n\n[23:45:10]\n🏠 地址: 福建省厦门市集美区洪塘头村洪塘大道\n\n[23:45:10]\n🔄 后端计算: 洪塘头站 → 酱文化园站\n\n[23:45:10]\n📊 状态: 已到站\n\n[23:45:10]\n✅ 50米到站检测成功: 洪塘头站\n\n[23:45:10]\n📋 已过站点更新: 6个站点\n\n[23:45:10]\n✅ 已过站点记录验证通过\n\n[23:45:10]\n🔄 Redis状态更新: 站点索引6\n\n[23:45:10]\n📋 已过站点: 6个\n\n[23:45:10]\n📊 统计更新: 总计7, 成功7, 失败0\n\n[23:45:15]\n🎯 自动测试步骤 8: 前往 酱文化园站 (索引7)\n\n[23:45:15]\n📍 移动到 酱文化园站: (24.649582, 118.143610) 距离: 2737米\n\n[23:45:15]\n✅ 位置数据发送成功: 车辆1162\n\n[23:45:15]\n📍 坐标: (24.649582, 118.143610)\n\n[23:45:15]\n🏠 地址: 福建省厦门市集美区酱文化园园区内\n\n[23:45:15]\n🔄 后端计算: 酱文化园站 → 内厝站\n\n[23:45:15]\n📊 状态: 已到站\n\n[23:45:15]\n✅ 50米到站检测成功: 酱文化园站\n\n[23:45:15]\n📋 已过站点更新: 7个站点\n\n[23:45:15]\n✅ 已过站点记录验证通过\n\n[23:45:15]\n🔄 Redis状态更新: 站点索引7\n\n[23:45:15]\n📋 已过站点: 7个\n\n[23:45:15]\n📊 统计更新: 总计8, 成功8, 失败0\n\n[23:45:20]\n🎯 自动测试步骤 9: 前往 内厝站 (索引8)\n\n[23:45:20]\n📍 移动到 内厝站: (24.665444, 118.273721) 距离: 13266米\n\n[23:45:20]\n✅ 位置数据发送成功: 车辆1162\n\n[23:45:20]\n📍 坐标: (24.665444, 118.273721)\n\n[23:45:20]\n🏠 地址: 福建省厦门市集美区内厝镇政府附近\n\n[23:45:20]\n🔄 后端计算: 内厝站 → 厦华科技有限公司\n\n[23:45:20]\n📊 状态: 已到站\n\n[23:45:20]\n✅ 50米到站检测成功: 内厝站\n\n[23:45:20]\n📋 已过站点更新: 8个站点\n\n[23:45:20]\n✅ 已过站点记录验证通过\n\n[23:45:20]\n🔄 Redis状态更新: 站点索引8\n\n[23:45:20]\n📋 已过站点: 8个\n\n[23:45:20]\n📊 统计更新: 总计9, 成功9, 失败0\n\n[23:45:25]\n🎯 自动测试步骤 10: 前往 厦华科技有限公司 (索引9)\n\n[23:45:25]\n📍 移动到 厦华科技有限公司: (24.675480, 118.280414) 距离: 1305米\n\n[23:45:25]\n✅ 位置数据发送成功: 车辆1162\n\n[23:45:25]\n📍 坐标: (24.675480, 118.280414)\n\n[23:45:25]\n🏠 地址: 福建省厦门市集美区厦华科技有限公司园区\n\n[23:45:25]\n🔄 后端计算: 厦华科技有限公司 → 已到站\n\n[23:45:25]\n📊 状态: 已到站\n\n[23:45:25]\n✅ 50米到站检测成功: 厦华科技有限公司\n\n[23:45:25]\n📋 已过站点更新: 9个站点\n\n[23:45:25]\n✅ 已过站点记录验证通过\n\n[23:45:25]\n🔄 Redis状态更新: 站点索引9\n\n[23:45:25]\n📋 已过站点: 9个\n\n[23:45:25]\n📊 统计更新: 总计10, 成功10, 失败0\n\n[23:45:25]\n🔄 完成第 1 个完整循环，重新从总站开始\n\n[23:45:25]\n📊 循环统计: 已完成1个循环，访问10个站点\n\n[23:45:30]\n🎯 自动测试步骤 11: 前往 总站(班车总站) (索引0)\n\n[23:45:30]\n📍 移动到总站: (24.58, 118.1)\n\n[23:45:30]\n✅ 位置数据发送成功: 车辆1162\n\n[23:45:30]\n📍 坐标: (24.580000, 118.100000)\n\n[23:45:30]\n🏠 地址: 福建省厦门市集美区班车总站停车场\n\n[23:45:30]\n🔄 后端计算: 总站(班车总站) → 嘉庚体育馆站\n\n[23:45:30]\n📊 状态: 已到站\n\n[23:45:30]\n✅ 50米到站检测成功: 总站(班车总站)\n\n[23:45:30]\n📋 已过站点更新: 0个站点\n\n[23:45:30]\n✅ 已过站点记录验证通过\n\n[23:45:30]\n🔄 Redis状态更新: 站点索引0\n\n[23:45:30]\n📋 已过站点: 0个\n\n[23:45:30]\n📊 统计更新: 总计11, 成功11, 失败0\n\n[23:45:33]\n💓 自动测试心跳: 车辆1162 第2轮测试进行中\n\n[23:45:35]\n🎯 自动测试步骤 12: 前往 嘉庚体育馆站 (索引1)\n\n[23:45:35]\n📍 移动到 嘉庚体育馆站: (24.587739, 118.106929) 距离: 1110米\n\n[23:45:35]\n✅ 位置数据发送成功: 车辆1162\n\n[23:45:35]\n📍 坐标: (24.587739, 118.106929)\n\n[23:45:35]\n🏠 地址: 福建省厦门市集美区嘉庚路199号嘉庚体育馆\n\n[23:45:35]\n🔄 后端计算: 嘉庚体育馆站 → 集美旧厂区站\n\n[23:45:35]\n📊 状态: 已到站\n\n[23:45:35]\n✅ 50米到站检测成功: 嘉庚体育馆站\n\n[23:45:35]\n📋 已过站点更新: 1个站点\n\n[23:45:35]\n✅ 已过站点记录验证通过\n\n[23:45:35]\n🔄 Redis状态更新: 站点索引1\n\n[23:45:35]\n📋 已过站点: 1个\n\n[23:45:35]\n📊 统计更新: 总计12, 成功12, 失败0\n\n[23:45:40]\n🎯 自动测试步骤 13: 前往 集美旧厂区站 (索引2)\n\n[23:45:40]\n📍 移动到 集美旧厂区站: (24.596259, 118.097248) 距离: 1362米\n\n[23:45:40]\n✅ 位置数据发送成功: 车辆1162\n\n[23:45:40]\n📍 坐标: (24.596259, 118.097248)\n\n[23:45:40]\n🏠 地址: 福建省厦门市集美区杏林湾路集美旧厂区\n\n[23:45:40]\n🔄 后端计算: 集美旧厂区站 → 霞梧路口站\n\n[23:45:40]\n📊 状态: 已到站\n\n[23:45:40]\n✅ 50米到站检测成功: 集美旧厂区站\n\n[23:45:40]\n📋 已过站点更新: 2个站点\n\n[23:45:40]\n✅ 已过站点记录验证通过\n\n[23:45:40]\n🔄 Redis状态更新: 站点索引2\n\n[23:45:40]\n📋 已过站点: 2个\n\n[23:45:40]\n📊 统计更新: 总计13, 成功13, 失败0\n\n[23:45:45]\n🎯 自动测试步骤 14: 前往 霞梧路口站 (索引3)\n\n[23:45:45]\n📍 移动到 霞梧路口站: (24.575946, 118.097412) 距离: 2259米\n\n[23:45:45]\n✅ 位置数据发送成功: 车辆1162\n\n[23:45:45]\n📍 坐标: (24.575946, 118.097412)\n\n[23:45:45]\n🏠 地址: 福建省厦门市集美区霞梧路与杏林北路交叉口\n\n[23:45:45]\n🔄 后端计算: 霞梧路口站 → 叶厝站\n\n[23:45:45]\n📊 状态: 已到站\n\n[23:45:45]\n✅ 50米到站检测成功: 霞梧路口站\n\n[23:45:45]\n📋 已过站点更新: 3个站点\n\n[23:45:45]\n✅ 已过站点记录验证通过\n\n[23:45:45]\n🔄 Redis状态更新: 站点索引3\n\n[23:45:45]\n📋 已过站点: 3个\n\n[23:45:45]\n📊 统计更新: 总计14, 成功14, 失败0\n\n[23:45:50]\n🎯 自动测试步骤 15: 前往 叶厝站 (索引4)\n\n[23:45:50]\n📍 移动到 叶厝站: (24.594295, 118.106670) 距离: 2245米\n\n[23:45:50]\n✅ 位置数据发送成功: 车辆1162\n\n[23:45:50]\n📍 坐标: (24.594295, 118.106670)\n\n[23:45:50]\n🏠 地址: 福建省厦门市集美区叶厝村委会附近\n\n[23:45:50]\n🔄 后端计算: 叶厝站 → 禹州大学城站\n\n[23:45:50]\n📊 状态: 已到站\n\n[23:45:50]\n✅ 50米到站检测成功: 叶厝站\n\n[23:45:50]\n📋 已过站点更新: 4个站点\n\n[23:45:50]\n✅ 已过站点记录验证通过\n\n[23:45:50]\n🔄 Redis状态更新: 站点索引4\n\n[23:45:50]\n📋 已过站点: 4个\n\n[23:45:50]\n📊 统计更新: 总计15, 成功15, 失败0\n\n[23:45:55]\n🎯 自动测试步骤 16: 前往 禹州大学城站 (索引5)\n\n[23:45:55]\n📍 移动到 禹州大学城站: (24.621937, 118.128021) 距离: 3756米\n\n[23:45:55]\n✅ 位置数据发送成功: 车辆1162\n\n[23:45:55]\n📍 坐标: (24.621937, 118.128021)\n\n[23:45:55]\n🏠 地址: 福建省厦门市集美区禹州大学城商业街\n\n[23:45:55]\n🔄 后端计算: 禹州大学城站 → 洪塘头站\n\n[23:45:55]\n📊 状态: 已到站\n\n[23:45:55]\n✅ 50米到站检测成功: 禹州大学城站\n\n[23:45:55]\n📋 已过站点更新: 5个站点\n\n[23:45:55]\n✅ 已过站点记录验证通过\n\n[23:45:55]\n🔄 Redis状态更新: 站点索引5\n\n[23:45:55]\n📋 已过站点: 5个\n\n[23:45:55]\n📊 统计更新: 总计16, 成功16, 失败0\n\n[23:46:00]\n🎯 自动测试步骤 17: 前往 洪塘头站 (索引6)\n\n[23:46:00]\n📍 移动到 洪塘头站: (24.627755, 118.130988) 距离: 713米\n\n[23:46:00]\n✅ 位置数据发送成功: 车辆1162\n\n[23:46:00]\n📍 坐标: (24.627755, 118.130988)\n\n[23:46:00]\n🏠 地址: 福建省厦门市集美区洪塘头村洪塘大道\n\n[23:46:00]\n🔄 后端计算: 洪塘头站 → 酱文化园站\n\n[23:46:00]\n📊 状态: 已到站\n\n[23:46:00]\n✅ 50米到站检测成功: 洪塘头站\n\n[23:46:00]\n📋 已过站点更新: 6个站点\n\n[23:46:00]\n✅ 已过站点记录验证通过\n\n[23:46:00]\n🔄 Redis状态更新: 站点索引6\n\n[23:46:00]\n📋 已过站点: 6个\n\n[23:46:00]\n📊 统计更新: 总计17, 成功17, 失败0\n\n[23:46:03]\n💓 自动测试心跳: 车辆1162 第2轮测试进行中\n\n[23:46:05]\n🎯 自动测试步骤 18: 前往 酱文化园站 (索引7)\n\n[23:46:05]\n📍 移动到 酱文化园站: (24.649631, 118.143591) 距离: 2746米\n\n[23:46:05]\n✅ 位置数据发送成功: 车辆1162\n\n[23:46:05]\n📍 坐标: (24.649631, 118.143591)\n\n[23:46:05]\n🏠 地址: 福建省厦门市集美区酱文化园园区内\n\n[23:46:05]\n🔄 后端计算: 酱文化园站 → 内厝站\n\n[23:46:05]\n📊 状态: 已到站\n\n[23:46:05]\n✅ 50米到站检测成功: 酱文化园站\n\n[23:46:05]\n📋 已过站点更新: 7个站点\n\n[23:46:05]\n✅ 已过站点记录验证通过\n\n[23:46:05]\n🔄 Redis状态更新: 站点索引7\n\n[23:46:05]\n📋 已过站点: 7个\n\n[23:46:05]\n📊 统计更新: 总计18, 成功18, 失败0\n\n[23:46:10]\n🎯 自动测试步骤 19: 前往 内厝站 (索引8)\n\n[23:46:10]\n📍 移动到 内厝站: (24.665490, 118.273726) 距离: 13269米\n\n[23:46:10]\n✅ 位置数据发送成功: 车辆1162\n\n[23:46:10]\n📍 坐标: (24.665490, 118.273726)\n\n[23:46:10]\n🏠 地址: 福建省厦门市集美区内厝镇政府附近\n\n[23:46:10]\n🔄 后端计算: 内厝站 → 厦华科技有限公司\n\n[23:46:10]\n📊 状态: 已到站\n\n[23:46:10]\n✅ 50米到站检测成功: 内厝站\n\n[23:46:10]\n📋 已过站点更新: 8个站点\n\n[23:46:10]\n✅ 已过站点记录验证通过\n\n[23:46:10]\n🔄 Redis状态更新: 站点索引8\n\n[23:46:10]\n📋 已过站点: 8个\n\n[23:46:10]\n📊 统计更新: 总计19, 成功19, 失败0\n\n[23:46:15]\n🎯 自动测试步骤 20: 前往 厦华科技有限公司 (索引9)\n\n[23:46:15]\n📍 移动到 厦华科技有限公司: (24.675545, 118.280366) 距离: 1304米\n\n[23:46:15]\n✅ 位置数据发送成功: 车辆1162\n\n[23:46:15]\n📍 坐标: (24.675545, 118.280366)\n\n[23:46:15]\n🏠 地址: 福建省厦门市集美区厦华科技有限公司园区\n\n[23:46:15]\n🔄 后端计算: 厦华科技有限公司 → 已到站\n\n[23:46:15]\n📊 状态: 已到站\n\n[23:46:15]\n✅ 50米到站检测成功: 厦华科技有限公司\n\n[23:46:15]\n📋 已过站点更新: 9个站点\n\n[23:46:15]\n✅ 已过站点记录验证通过\n\n[23:46:15]\n🔄 Redis状态更新: 站点索引9\n\n[23:46:15]\n📋 已过站点: 9个\n\n[23:46:15]\n📊 统计更新: 总计20, 成功20, 失败0\n\n[23:46:15]\n🔄 完成第 2 个完整循环，重新从总站开始\n\n[23:46:15]\n📊 循环统计: 已完成2个循环，访问20个站点\n\n[23:46:20]\n🎯 自动测试步骤 21: 前往 总站(班车总站) (索引0)\n\n[23:46:20]\n📍 移动到总站: (24.58, 118.1)\n\n[23:46:20]\n✅ 位置数据发送成功: 车辆1162\n\n[23:46:20]\n📍 坐标: (24.580000, 118.100000)\n\n[23:46:20]\n🏠 地址: 福建省厦门市集美区班车总站停车场\n\n[23:46:20]\n🔄 后端计算: 总站(班车总站) → 嘉庚体育馆站\n\n[23:46:20]\n📊 状态: 已到站\n\n[23:46:20]\n✅ 50米到站检测成功: 总站(班车总站)\n\n[23:46:20]\n📋 已过站点更新: 0个站点\n\n[23:46:20]\n✅ 已过站点记录验证通过\n\n[23:46:20]\n🔄 Redis状态更新: 站点索引0\n\n[23:46:20]\n📋 已过站点: 0个\n\n[23:46:20]\n📊 统计更新: 总计21, 成功21, 失败0\n\n[23:46:21]\n⏹️ 自动测试已停止 - 运行时长: 101秒\n\n[23:46:21]\n📊 测试统计: 成功21/21次更新, 访问21个站点, 完成2个循环\n\n[23:46:22]\n🔍 当前统计数据: {\"totalUpdates\":0,\"successfulUpdates\":0,\"failedUpdates\":0,\"stationsVisited\":0,\"cyclesCompleted\":0}\n\n[23:46:22]\n📊 === 自动测试报告 ===\n\n[23:46:22]\n🕐 测试开始时间: 2025/8/6 23:44:40\n\n[23:46:22]\n⏱️ 测试持续时间: 1分43秒\n\n[23:46:22]\n📈 更新统计: 21/21 (成功率: 100%)\n\n[23:46:22]\n🚏 访问站点: 21个\n\n[23:46:22]\n🔄 完成循环: 2次\n\n[23:46:22]\n⚡ 平均间隔: 5秒/次\n\n[23:46:22]\n📊 测试状态: 已完成\n\n[23:46:23]\n🔍 数据完整性: 正常\n\n[23:46:23]\n🐛 调试信息: {\"autoTestStatsSnapshot\":\"{\\\"totalUpdates\\\":0,\\\"successfulUpdates\\\":0,\\\"failedUpdates\\\":0,\\\"stationsVisited\\\":0,\\\"cyclesCompleted\\\":0}\",\"globalStatsSnapshot\":\"{\\\"totalUpdates\\\":21,\\\"successfulUpdates\\\":21,\\\"stationsVisited\\\":21,\\\"failedUpdates\\\":0,\\\"cyclesCompleted\\\":2}\",\"testRunning\":false,\"startTime\":1754495080182}\n\n[23:46:23]\n📊 === 报告结束 ===\n\n[23:46:29]\n🔍 当前统计数据: {\"totalUpdates\":0,\"successfulUpdates\":0,\"failedUpdates\":0,\"stationsVisited\":0,\"cyclesCompleted\":0}\n\n[23:46:29]\n📊 === 自动测试报告 ===\n\n[23:46:29]\n🕐 测试开始时间: 2025/8/6 23:44:40\n\n[23:46:29]\n⏱️ 测试持续时间: 1分49秒\n\n[23:46:29]\n📈 更新统计: 21/21 (成功率: 100%)\n\n[23:46:29]\n🚏 访问站点: 21个\n\n[23:46:29]\n🔄 完成循环: 2次\n\n[23:46:29]\n⚡ 平均间隔: 5秒/次\n\n[23:46:29]\n📊 测试状态: 已完成\n\n[23:46:29]\n🔍 数据完整性: 正常\n\n[23:46:29]\n🐛 调试信息: {\"autoTestStatsSnapshot\":\"{\\\"totalUpdates\\\":0,\\\"successfulUpdates\\\":0,\\\"failedUpdates\\\":0,\\\"stationsVisited\\\":0,\\\"cyclesCompleted\\\":0}\",\"globalStatsSnapshot\":\"{\\\"totalUpdates\\\":21,\\\"successfulUpdates\\\":21,\\\"stationsVisited\\\":21,\\\"failedUpdates\\\":0,\\\"cyclesCompleted\\\":2}\",\"testRunning\":false,\"startTime\":1754495080182}\n\n[23:46:29]\n📊 === 报告结束 ===\n", "timestamp": "2025-08-06T15:46:29.248Z"}
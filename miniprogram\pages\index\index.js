// pages/index/index.js
const app = getApp()
const request = require('../../utils/request')
const stationConfig = require('../../utils/stationConfig')

Page({
  data: {
    routes: [],
    isLoading: true,
    CurrentDate: '',
    isInTimeRange: false, // 是否在可查询时间范围内
    hasError: false,
    errorMessage: '',
    showRetry: false
  },

  onLoad() {
    console.log('首页onLoad开始')
    try {
      // 初始化
      this.checkTimeRange()
      
      // 设置初始loading状态
      this.setData({ isLoading: true })
      
      // 设置加载超时，防止一直loading
      this.setupLoadingTimeout()
      
      // 尝试获取数据
      this.initializeData()
      
      console.log('首页onLoad完成')
    } catch (error) {
      console.error('首页onLoad错误:', error)
      this.handleError('页面初始化失败', error)
    }
  },

  // 初始化数据
  async initializeData() {
    try {
      // 如果全局服务已有数据，立即使用
      const globalLocations = app.getAllVehicleLocations()
      if (globalLocations.length > 0) {
        console.log('使用全局缓存的车辆位置数据')
        this.updateRoutesFromGlobalData(globalLocations)
        return
      }

      // 没有全局数据，主动获取
      console.log('没有全局数据，主动获取')
      await this.loadRoutes()
      
      // 如果还是没有数据，等待一段时间看看全局服务是否会提供数据
      if (this.data.routes.length === 0 && this.data.isLoading) {
        console.log('等待全局服务提供数据...')
        setTimeout(() => {
          const globalLocations2 = app.getAllVehicleLocations()
          if (globalLocations2.length > 0) {
            console.log('延迟获取到全局数据')
            this.updateRoutesFromGlobalData(globalLocations2)
          } else if (this.data.isLoading) {
            // 如果还是没有数据，显示友好提示
            this.setData({
              isLoading: false,
              hasError: false,
              routes: [],
              errorMessage: ''
            })
            console.log('没有可用的班车数据')
          }
        }, 2000)
      }
    } catch (error) {
      console.error('初始化数据失败:', error)
      this.handleError('数据获取失败', error)
    }
  },

  // 设置加载超时机制
  setupLoadingTimeout() {
    // 6秒后如果还在loading，强制停止并显示错误
    this.loadingTimeout = setTimeout(() => {
      if (this.data.isLoading) {
        console.warn('加载超时，强制停止loading')
        this.setData({
          isLoading: false,
          hasError: true,
          errorMessage: '数据加载超时，请检查网络连接',
          showRetry: true
        })
      }
    }, 6000)
  },

  // 清除加载超时
  clearLoadingTimeout() {
    if (this.loadingTimeout) {
      clearTimeout(this.loadingTimeout)
      this.loadingTimeout = null
    }
  },

  onShow() {
    // 刷新时间状态
    this.checkTimeRange()
    
    // 使用全局数据，不再主动刷新
    const globalLocations = app.getAllVehicleLocations()
    if (globalLocations.length > 0) {
      console.log('使用全局车辆位置数据')
      this.updateRoutesFromGlobalData(globalLocations)
    } else {
      // 只有在没有全局数据时才主动获取
      this.loadRoutes()
    }
  },

  // 全局位置更新回调（由app.js调用）
  onGlobalLocationUpdate(locations) {
    console.log('首页收到全局位置更新:', locations ? locations.length : 0, '个车辆')
    
    // 如果收到空数组，说明全局服务可能失败，需要使用fallback
    if (!locations || locations.length === 0) {
      console.log('全局数据为空，可能是长轮询失败，尝试直接获取数据')
      if (this.data.isLoading) {
        this.loadRoutes()
      }
      return
    }
    
    this.updateRoutesFromGlobalData(locations)
  },

  // 使用全局数据更新路线
  updateRoutesFromGlobalData(locations) {
    console.log('updateRoutesFromGlobalData被调用，数据:', locations)
    
    if (!Array.isArray(locations) || locations.length === 0) {
      console.log('全局数据为空，尝试主动获取数据')
      // 如果全局数据为空且当前正在loading，尝试主动获取
      if (this.data.isLoading) {
        this.loadRoutes()
      }
      return
    }

    try {
      const routes = this.transformLocationsToRoutes(locations)
      
      // 清除超时定时器
      this.clearLoadingTimeout()
      
      this.setData({
        routes: routes,
        isLoading: false,
        hasError: false,
        showRetry: false
      })
      console.log('使用全局数据更新线路成功，共', routes.length, '条线路')
    } catch (error) {
      console.error('更新全局数据失败:', error)
      // 如果处理全局数据失败，尝试主动获取
      this.loadRoutes()
    }
  },

  // 检查时间范围（6:00-9:00）
  checkTimeRange() {
    const now = new Date()
    const hour = now.getHours()
    //const isInRange = hour >= 6 && hour < 9
    const isInRange =true;
    const year = now.getFullYear();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const dateStr = `${year}-${month}-${day}`;
    this.setData({
      CurrentDate: dateStr
    });
    this.setData({
      isInTimeRange: isInRange
    })

    console.log(`当前时间: ${hour}:${now.getMinutes()}, 是否在查询范围内: ${isInRange}`)
  },




  // 从后端API加载数据
  async loadRoutes() {
    console.log('开始从后端加载线路数据')
    this.setData({
      isLoading: true,
      hasError: false,
      errorMessage: '',
      showRetry: false
    })

    try {
      // 调用后端API获取所有位置信息
      const response = await request.get('/vehicle/locations')
      if (response.success && response.data) {
        // 转换后端数据为前端需要的格式
        const routes = this.transformLocationsToRoutes(response.data)
        
        // 清除超时定时器
        this.clearLoadingTimeout()
        
        this.setData({
          routes: routes,
          isLoading: false,
          hasError: false
        })
        
        console.log('线路数据加载成功，共', routes.length, '条线路')
        console.log('原始数据:', response.data)
      } else {
        throw new Error(response.message || '数据格式错误')
      }

    } catch (error) {
      console.error('加载数据失败:', error)
      this.clearLoadingTimeout() // 也要清除超时定时器
      this.handleError('数据加载失败', error)
    }
  },

  // 将后端locations数据转换为前端routes格式
  transformLocationsToRoutes(locations) {
    const colorPalette = ['#4ECDC4', '#FF6B6B', '#45B7D1', '#9B59B6', '#E67E22', '#F39C12', '#2ECC71', '#E74C3C']
    
    return locations.map((location, index) => {
      // 改进的在线状态判断逻辑
      // 1. 后端已经根据时间计算了 isOnline
      // 2. 状态码3表示离线，10表示运行中，9和13表示在线
      // 3. 综合两个条件判断最终在线状态
      let isOnline = false
      let statusText = '离线'
      
      if (location.isOnline) {
        // 后端判断为在线时，进一步检查状态码
        switch (location.state) {
          case 3:
            isOnline = false
            statusText = '离线'
            break
          case 9:
            isOnline = true
            statusText = '在线'
            break
          case 10:
            isOnline = true
            statusText = '运行中'
            break
          case 13:
            isOnline = true
            statusText = '在线'
            break
          default:
            // 对于其他状态码，如果后端判断为在线且有有效位置数据，认为在线
            isOnline = location.lat > 0 && location.lng > 0
            statusText = isOnline ? '在线' : '离线'
            break
        }
      } else {
        // 后端判断为离线
        isOnline = false
        statusText = '离线'
      }
      
      // 获取线路配置
      const lineConfig = stationConfig.getLineConfig(location.carId)
      const isDetailEnabled = stationConfig.isDetailEnabled(location.carId)
      
      // 计算当前站点和下一站信息（直接使用后端提供的数据）
      let currentLocationText = location.currentStation || '获取中...'
      let nextStationText = location.nextStation || '计算中...'
      
      // 如果启用详细功能，使用更精确的描述
      if (isDetailEnabled) {
        // 后端已经计算好了当前站点和下一站，直接使用
        if (location.currentStation) {
          currentLocationText = location.currentStation
        }
        if (location.nextStation && location.nextStation !== '前端计算中') {
          nextStationText = location.nextStation
        } else {
          nextStationText = '计算中...'
        }
      } else {
        // 对于未启用详细功能的线路，显示简单信息
        currentLocationText = location.currentStation || '位置获取中...'
        nextStationText = ''
      }
      
      return {
        id: location.carId,
        name: location.carLine,
        carNumber: location.carName,
        color: colorPalette[index % colorPalette.length],
        isActive: isOnline,
        status: isOnline ? 'available' : 'offline',
        statusText: statusText,
        currentLocation: currentLocationText,
        nextStation: nextStationText,
        isDetailEnabled: isDetailEnabled,
        // 保留原始数据以备后用
        originalData: location,
        // 使用配置的站点信息（如果有的话）
        stations: lineConfig ? lineConfig.stations.map((station, idx) => ({
          id: station.id,
          name: station.name,
          order: station.order,
          latitude: station.latitude,
          longitude: station.longitude,
          isPassed: false
        })) : [
          {
            id: 1,
            name: location.nextStation || '当前位置',
            order: 1,
            latitude: location.lat,
            longitude: location.lng,
            isPassed: false
          }
        ]
      }
    })
  },

  // 错误处理函数
  handleError(message, error) {
    console.error(message, error)

    // 设置错误状态
    this.setData({
      isLoading: false,
      hasError: true,
      errorMessage: message || '数据无法获取',
      showRetry: true,
      routes: [] // 确保routes为空数组
    })

    // 显示错误提示
    wx.showToast({
      title: message || '数据加载失败',
      icon: 'none',
      duration: 2000
    })
  },

  // 重试加载数据
  retryLoad() {
    console.log('用户点击重试')
    this.setData({
      hasError: false,
      errorMessage: '',
      showRetry: false
    })
    this.checkTimeRange()
    this.loadRoutes()
  },

  // 简单刷新
  refreshData() {
    this.checkTimeRange()
    if (this.data.isInTimeRange) {
      this.loadRoutes()
    } else {
      // 即使不在时间范围内，也要清除错误状态
      this.setData({
        hasError: false,
        errorMessage: '',
        showRetry: false,
        isLoading: false
      })
    }
  },

  // 点击线路卡片
  onRouteClick(e) {
    const route = e.currentTarget.dataset.route

    // 检查是否在服务时间范围内
    if (!this.data.isInTimeRange) {
      wx.showToast({
        title: '不在服务时间范围内',
        icon: 'none',
        duration: 2000
      })
      return
    }

    // 检查车辆是否在线
    if (!route.isActive || route.status === 'offline') {
      wx.showModal({
        title: '离线提醒',
        content: '当前班车处于离线，无法查看实时位置',
        showCancel: false,
        confirmText: '知道了'
      })
      return
    }

    if (route.isActive) {
      // 检查是否启用详细功能
      if (!route.isDetailEnabled) {
        // 显示开发中提示
        wx.showModal({
          title: '提示',
          content: '正在进一步开发中...',
          showCancel: false,
          confirmText: '知道了'
        })
        return
      }
      
      // 跳转到线路详情页，传递完整的线路数据
      const routeData = encodeURIComponent(JSON.stringify(route))
      wx.navigateTo({
        url: `/pages/route/route?routeData=${routeData}`
      })
    } else {
      // 显示即将接入提示
      wx.showModal({
        title: '提示',
        content: route.statusText || '正在接入中，请稍后...',
        showCancel: false,
        confirmText: '知道了'
      })
    }
  },




  // 下拉刷新
  onPullDownRefresh() {
    this.refreshData()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  // 页面卸载时清理资源
  onUnload() {
    this.clearLoadingTimeout()
  }

})

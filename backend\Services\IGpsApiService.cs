using WeChatBus.Models;

namespace WeChatBus.Services;

/// <summary>
/// GPS API服务接口
/// </summary>
public interface IGpsApiService
{
    /// <summary>
    /// 获取车辆位置信息
    /// </summary>
    /// <param name="carIds">车辆ID列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>车辆位置信息列表</returns>
    Task<List<VehicleLocation>> GetVehicleLocationsAsync(List<string> carIds, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取所有配置的车辆位置信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>车辆位置信息列表</returns>
    Task<List<VehicleLocation>> GetAllVehicleLocationsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查GPS API服务是否可用
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>服务是否可用</returns>
    Task<bool> IsServiceAvailableAsync(CancellationToken cancellationToken = default);
}

// app.js
const { longPollingService } = require('./utils/longPolling')

App({
  globalData: {
    userInfo: null,
    isLoggedIn: false,
    // baseUrl: 'https://microcloud.iprima.com.cn/api/wechatbus/api',
    baseUrl: 'http://localhost:5000/api',
    // 全局车辆位置数据
    vehicleLocations: [],
    lastLocationUpdate: null,
    
    // 调试配置
    debugMode: false, // 开发时设为true，发布时设为false
    debugStartPage: '/pages/index/index', // 调试时的起始页面
    debugParams: { // 调试参数
      routeData: JSON.stringify({
        id: 1,
        name: '2号线',
        carNumber: '闽DZ5829',
        color: '#4ECDC4',
        stations: [
          { id: 0, name: '车队总站', order: 0, isPassed: true },
          { id: 1, name: '嘉庚体育馆', order: 1, isPassed: true },
          { id: 2, name: '集美旧厂区', order: 2, isPassed: true },
          { id: 3, name: '霞梧路口', order: 3, isPassed: true },
          { id: 4, name: '叶厝', order: 4, isPassed: true },
          { id: 5, name: '禹州大学城', order: 5, isPassed: false },
          { id: 6, name: '洪塘头', order: 6, isPassed: false },
          { id: 7, name: '古龙酱文化园', order: 7, isPassed: false },
          { id: 8, name: '内厝', order: 8, isPassed: false },
          { id: 9, name: '厦华科技', order: 9, isPassed: false },
        ]
      })
    }
  },

  onLaunch() {
    console.log('小程序启动')
    this.checkLocalUserInfo()

    // 处理隐私协议
    this.handlePrivacyAgreement()

    // 启动全局长轮询服务
    this.initGlobalLongPolling()

    // 调试模式下直接跳转
    if (this.globalData.debugMode && this.globalData.debugStartPage) {
      this.jumpToDebugPage()
    }
  },

  // 初始化全局长轮询服务
  initGlobalLongPolling() {
    console.log('初始化全局长轮询服务')
    
    // 设置全局数据回调
    longPollingService.onData(this.onGlobalLocationUpdate.bind(this))
    
    // 启动长轮询
    try {
      longPollingService.start()
      
      // 设置一个初始检查，如果5秒内没有数据，通知页面使用fallback
      setTimeout(() => {
        if (!this.globalData.vehicleLocations || this.globalData.vehicleLocations.length === 0) {
          console.warn('全局长轮询5秒内没有获取到数据，可能连接失败')
          // 通知页面使用fallback方式获取数据
          this.notifyLocationUpdateToPages([]) // 传递空数组，让页面自行处理
        }
      }, 5000)
      
    } catch (error) {
      console.error('启动全局长轮询失败:', error)
      // 通知页面使用fallback方式
      this.notifyLocationUpdateToPages([])
    }
  },

  // 全局位置数据更新回调
  onGlobalLocationUpdate(data, error) {
    if (error) {
      console.error('全局长轮询错误:', error)
      return
    }

    console.log('全局位置数据更新:', data)

    // 检查数据格式
    let locations = []
    if (data && data.locations && Array.isArray(data.locations)) {
      locations = data.locations
    } else if (data && Array.isArray(data)) {
      locations = data
    } else if (data && data.data && Array.isArray(data.data)) {
      locations = data.data
    }

    if (locations.length > 0) {
      // 更新全局数据
      this.globalData.vehicleLocations = locations
      this.globalData.lastLocationUpdate = new Date()
      
      console.log('全局位置数据已更新，车辆数量:', locations.length)
      
      // 通知所有注册的页面
      this.notifyLocationUpdateToPages(locations)
    }
  },

  // 通知页面位置更新
  notifyLocationUpdateToPages(locations) {
    const pages = getCurrentPages()
    pages.forEach(page => {
      // 如果页面有 onGlobalLocationUpdate 方法，则调用
      if (typeof page.onGlobalLocationUpdate === 'function') {
        try {
          page.onGlobalLocationUpdate(locations)
        } catch (error) {
          console.error('通知页面位置更新失败:', error)
        }
      }
    })
  },

  // 获取车辆位置数据
  getVehicleLocation(carId) {
    if (!this.globalData.vehicleLocations) return null
    
    return this.globalData.vehicleLocations.find(loc => 
      loc.carId === carId || loc.carId === String(carId)
    )
  },

  // 获取所有车辆位置数据
  getAllVehicleLocations() {
    return this.globalData.vehicleLocations || []
  },

  // 处理隐私协议
  handlePrivacyAgreement() {
    if (wx.getPrivacySetting) {
      wx.getPrivacySetting({
        success: res => {
          console.log('隐私设置:', res)
          if (res.needAuthorization) {
            // 需要弹出隐私协议
            this.showPrivacyModal()
          }
        },
        fail: err => {
          console.error('获取隐私设置失败:', err)
        }
      })
    }
  },

  // 显示隐私协议弹窗
  showPrivacyModal() {
    wx.showModal({
      title: '隐私保护指引',
      content: '感谢您使用厦华云班车小程序。我们非常重视您的隐私保护，在您使用我们的服务前，请仔细阅读《隐私保护指引》，了解我们如何收集、使用您的信息。',
      confirmText: '同意并继续',
      cancelText: '不同意',
      success: (res) => {
        if (res.confirm) {
          // 用户同意隐私协议
          console.log('用户同意隐私协议')
        } else {
          // 用户不同意，退出小程序
          wx.exitMiniProgram()
        }
      }
    })
  },

  jumpToDebugPage() {
    const { debugStartPage, debugParams } = this.globalData
    let url = debugStartPage
    
    // 添加参数
    if (debugParams && Object.keys(debugParams).length > 0) {
      const params = Object.entries(debugParams)
        .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
        .join('&')
      url += `?${params}`
    }
    
    wx.reLaunch({ url })
  },

  // 检查本地用户信息
  checkLocalUserInfo() {
    try {
      const userInfo = wx.getStorageSync('userInfo')
      if (userInfo) {
        this.globalData.userInfo = userInfo
        this.globalData.isLoggedIn = true
        console.log('发现本地用户信息:', userInfo)
      }
    } catch (error) {
      console.error('读取本地用户信息失败:', error)
    }
  },

  // 检查用户是否已登录（兼容旧版本调用）
  checkUserInfo() {
    return this.globalData.isLoggedIn && this.globalData.userInfo
  },

  // 获取用户信息（兼容旧版本调用）
  getUserInfo() {
    return this.globalData.userInfo || {
      nickName: '用户',
      avatarUrl: '',
      phoneNumber: ''
    }
  },

  // 保存用户登录信息
  saveUserInfo(userInfo) {
    try {
      this.globalData.userInfo = userInfo
      this.globalData.isLoggedIn = true
      wx.setStorageSync('userInfo', userInfo)
      console.log('用户信息保存成功:', userInfo)
    } catch (error) {
      console.error('保存用户信息失败:', error)
    }
  },

  // 清除用户信息
  clearUserInfo() {
    this.globalData.userInfo = null
    this.globalData.isLoggedIn = false
    try {
      wx.removeStorageSync('userInfo')
    } catch (error) {
      console.error('清除用户信息失败:', error)
    }
  },

  // 获取微信用户基本信息
  async getWeChatUserProfile() {
    try {
      if (!wx.getUserProfile) {
        throw new Error('当前微信版本不支持getUserProfile')
      }

      const res = await new Promise((resolve, reject) => {
        wx.getUserProfile({
          desc: '用于完善用户资料',
          success: resolve,
          fail: reject
        })
      })

      if (res.userInfo) {
        console.log('获取用户信息成功:', res.userInfo)
        this.globalData.userInfo = res.userInfo
        return res.userInfo
      } else {
        throw new Error('获取用户信息失败')
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw new Error('获取用户信息失败，请重试')
    }
  },

  // 网络请求方法
  request(options) {
    return new Promise((resolve, reject) => {
      const requestOptions = {
        url: `${this.globalData.baseUrl}${options.url}`,
        method: options.method || 'GET',
        data: options.data || {},
        header: {
          'Content-Type': 'application/json',
          ...options.header
        },
        timeout: 10000, // 10秒超时
        success: (res) => {
          console.log(`请求成功: ${options.method || 'GET'} ${options.url}`, res.data)
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(res.data)
          } else {
            const error = new Error(`HTTP ${res.statusCode}: ${res.data?.message || 'Request failed'}`)
            error.statusCode = res.statusCode
            error.data = res.data
            reject(error)
          }
        },
        fail: (err) => {
          console.error(`请求失败: ${options.method || 'GET'} ${options.url}`, err)
          reject(new Error(err.errMsg || '网络请求失败'))
        }
      }

      wx.request(requestOptions)
    })
  }
})


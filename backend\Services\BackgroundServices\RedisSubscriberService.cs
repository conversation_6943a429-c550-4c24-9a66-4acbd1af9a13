using StackExchange.Redis;
using WeChatBus.Services;

namespace WeChatBus.Services.BackgroundServices;

/// <summary>
/// Redis订阅服务
/// 订阅Redis频道并转发到内存事件总线
/// </summary>
public class RedisSubscriberService : BackgroundService
{
    private readonly IConnectionMultiplexer _redis;
    private readonly EventBus _eventBus;
    private readonly ILogger<RedisSubscriberService> _logger;

    public RedisSubscriberService(
        IConnectionMultiplexer redis,
        EventBus eventBus,
        ILogger<RedisSubscriberService> logger)
    {
        _redis = redis;
        _eventBus = eventBus;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Redis订阅服务启动");

        try
        {
            var subscriber = _redis.GetSubscriber();

            // 订阅位置更新频道
            await subscriber.SubscribeAsync(
                RedisChannel.Literal("wechat_bus:location_update"),
                (channel, message) =>
                {
                    try
                    {
                        _logger.LogDebug("收到Redis消息: Channel={Channel}", channel);
                        _eventBus.Publish(channel.ToString(), message.ToString());
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "处理Redis消息时发生异常");
                    }
                });

            _logger.LogInformation("成功订阅Redis频道: wechat_bus:location_update");

            // 保持服务运行
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(1000, stoppingToken);

                // 定期清理空频道
                if (DateTime.Now.Second % 30 == 0)
                {
                    _eventBus.CleanupEmptyChannels();
                }
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Redis订阅服务被取消");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis订阅服务发生错误");
        }

        _logger.LogInformation("Redis订阅服务停止");
    }
}
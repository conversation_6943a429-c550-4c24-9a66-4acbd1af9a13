using System.Text.Json.Serialization;
using WeChatBus.Services;

namespace WeChatBus.Models;

/// <summary>
/// 车辆位置信息模型
/// </summary>
public class VehicleLocation
{
    /// <summary>
    /// 车辆几号线
    /// </summary>
    public string CarLine { get; set; } = string.Empty;
    /// <summary>
    /// 车辆名称
    /// </summary>
    public string CarName { get; set; } = string.Empty;
    /// <summary>
    /// 车辆ID
    /// </summary>
    [JsonPropertyName("carId")]
    public string CarId { get; set; } = string.Empty;

    /// <summary>
    /// 纬度坐标
    /// </summary>
    [JsonPropertyName("lat")]
    public double Latitude { get; set; }

    /// <summary>
    /// 经度坐标
    /// </summary>
    [JsonPropertyName("lng")]
    public double Longitude { get; set; }

    /// <summary>
    /// 地址描述
    /// </summary>
    [JsonPropertyName("addr")]
    public string Address { get; set; } = string.Empty;

    /// <summary>
    /// 车辆速度 (km/h)
    /// </summary>
    [JsonPropertyName("speed")]
    public double Speed { get; set; }

    /// <summary>
    /// 位置更新时间
    /// </summary>
    [JsonPropertyName("time")]
    public string Time { get; set; } = string.Empty;
    /// <summary>
    /// 车辆状态
    /// </summary>
    [JsonPropertyName("state")]
    public int State { get; set; }

    /// <summary>
    /// 车辆是否在线，false表示离线，true表示在线
    /// </summary>
    public bool IsOnline { get; set; }
    /// <summary>
    /// 下一站名称
    /// </summary>
    public string NextStation { get; set; } = string.Empty;

    /// <summary>
    /// 当前站点名称
    /// </summary>
    public string CurrentStation { get; set; } = string.Empty;

    /// <summary>
    /// 已过站点ID列表（JSON字符串）
    /// </summary>
    public string PassedStationIds { get; set; } = string.Empty;

    /// <summary>
    /// 当前站点索引
    /// </summary>
    public int CurrentStationIndex { get; set; } = -1;

    /// <summary>
    /// 是否已到站（在50米范围内）
    /// </summary>
    public bool IsAtStation { get; set; } = false;

    /// <summary>
    /// 数据更新时间戳
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    /// <summary>
    /// 根据CarId映射设置CarLine、CarName，并根据Time判断IsOnline，计算站点状态
    /// </summary>
    public void ApplyMappingAndStatus(IRedisService? redisService = null)
    {
        switch (CarId)
        {
            case "1181":
                CarLine = "1号线";
                CarName = "闽DY1576";
                break;
            case "1162":
                CarLine = "2号线";
                CarName = "闽DZ5829";
                break;
            case "1171":
                CarLine = "3号线";
                CarName = "闽DX1686";
                break;
            case "5699":
                CarLine = "4号线";
                CarName = "闽DX3180";
                break;
            case "1168":
                CarLine = "5号线";
                CarName = "闽DZ9581";
                break;
            default:
                CarLine = string.Empty;
                CarName = string.Empty;
                break;
        }

        // 判断是否在线
        if (DateTime.TryParse(Time, out var updateTime))
            IsOnline = (DateTime.UtcNow - updateTime.ToUniversalTime()) <= TimeSpan.FromMinutes(5);
        else
            IsOnline = false;
        
        // 计算站点状态 - 根据车辆位置判断已过站点
        if (Latitude == 0 || Longitude == 0 || !IsOnline)
        {
            NextStation = "位置信息获取中";
            CurrentStation = "位置获取中";
            CurrentStationIndex = -1;
            PassedStationIds = "[]";
        }
        else
        {
            // 计算当前位置对应的站点状态
            CalculateStationStatus(redisService);
        }
    }

    /// <summary>
    /// 根据车辆位置计算站点状态（简化的状态机逻辑）
    /// </summary>
    private void CalculateStationStatus(IRedisService? redisService = null)
    {
        // 获取对应线路的站点配置
        var stations = GetStationsByCarId(CarId);
        if (stations == null || !stations.Any())
        {
            NextStation = "前端计算中";
            CurrentStation = "前端计算中";
            CurrentStationIndex = -1;
            PassedStationIds = "[]";
            return;
        }

        // 找到有效坐标的站点（排除总站）
        var validStations = stations.Where(s => s.Lat != 0.0 && s.Lng != 0.0).ToArray();
        if (!validStations.Any())
        {
            NextStation = "前端计算中";
            CurrentStation = "前端计算中";
            CurrentStationIndex = -1;
            PassedStationIds = "[]";
            return;
        }

        // 查找最近站点
        var nearestStation = validStations.OrderBy(s => CalculateDistance(Latitude, Longitude, s.Lat, s.Lng)).First();
        var nearestDistance = CalculateDistance(Latitude, Longitude, nearestStation.Lat, nearestStation.Lng);
        var nearestIndex = nearestStation.Order;

        var passedStationIds = new List<string>();

        // 简化的状态机逻辑
        if (nearestDistance <= 50) 
        {
            // 状态：已到站
            CurrentStation = nearestStation.Name;
            CurrentStationIndex = nearestIndex;
            IsAtStation = true;
            
            // 标记已过站点：当前站点之前的所有站点
            for (int i = 0; i < nearestIndex; i++)
            {
                passedStationIds.Add(stations[i].Id);
            }
            
            // 设置下一站
            NextStation = (nearestIndex < stations.Length - 1) ? stations[nearestIndex + 1].Name : "已到站";
            
            // 保存到站状态到Redis缓存（异步执行，不阻塞主流程）
            if (redisService != null)
            {
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await redisService.SaveStationArrivalAsync(CarId, nearestStation.Id, DateTime.UtcNow);
                    }
                    catch (Exception ex)
                    {
                        // 记录日志但不影响主流程
                        Console.WriteLine($"保存到站记录失败: {ex.Message}");
                    }
                });
            }
        }
        else if (nearestDistance <= 300)
        {
            // 状态：开往该站（接近中）
            CurrentStation = $"开往{nearestStation.Name}";
            CurrentStationIndex = Math.Max(0, nearestIndex - 1);
            IsAtStation = false;
            NextStation = nearestStation.Name;
            
            // 已过站点：开往目标站点的前一个站点之前
            for (int i = 0; i < nearestIndex - 1; i++)
            {
                if (i >= 0 && i < stations.Length)
                {
                    passedStationIds.Add(stations[i].Id);
                }
            }
        }
        else
        {
            // 状态：在总站或路上
            var firstStationWithCoords = validStations.OrderBy(s => s.Order).FirstOrDefault();
            if (firstStationWithCoords != null)
            {
                var distanceToFirst = CalculateDistance(Latitude, Longitude, firstStationWithCoords.Lat, firstStationWithCoords.Lng);
                
                if (distanceToFirst > 500) // 距离第一站超过500米，认为在总站
                {
                    CurrentStation = stations.FirstOrDefault(s => s.IsTerminal)?.Name ?? "总站(班车总站)";
                    CurrentStationIndex = 0;
                    NextStation = firstStationWithCoords.Name;
                    IsAtStation = true; // 在总站也算已到站
                    
                    // 关键：确保总站使用班车实时坐标，而不是固定坐标
                    // 这样前端计算到第一站的距离时，用的就是班车当前真实位置
                    var terminalStation = stations.FirstOrDefault(s => s.IsTerminal);
                    if (terminalStation != null)
                    {
                        // 动态更新总站坐标为班车实时位置
                        terminalStation.Lat = Latitude;
                        terminalStation.Lng = Longitude;
                    }
                    
                    // 在总站时没有已过站点
                }
                else
                {
                    // 在路上，开往最近的站点
                    CurrentStation = $"开往{nearestStation.Name}";
                    CurrentStationIndex = Math.Max(0, nearestIndex - 1);
                    NextStation = nearestStation.Name;
                    IsAtStation = false;
                    
                    // 设置已过站点
                    for (int i = 0; i < CurrentStationIndex; i++)
                    {
                        passedStationIds.Add(stations[i].Id);
                    }
                }
            }
        }

        PassedStationIds = System.Text.Json.JsonSerializer.Serialize(passedStationIds);
    }

    /// <summary>
    /// 根据车辆ID获取站点配置
    /// </summary>
    private BusStationModel[]? GetStationsByCarId(string carId)
    {
        // 2号线站点配置（与前端保持一致）
        if (carId == "1162")
        {
            return new[]
            {
                new BusStationModel { Id = "line2_station0", Name = "总站(班车总站)", Order = 0, Lat = 0.0, Lng = 0.0, IsTerminal = true },
                new BusStationModel { Id = "line2_station1", Name = "嘉庚体育馆站", Order = 1, Lat = 24.587788, Lng = 118.106925, IsTerminal = false },
                new BusStationModel { Id = "line2_station2", Name = "集美旧厂区站", Order = 2, Lat = 24.596244, Lng = 118.097278, IsTerminal = false },
                new BusStationModel { Id = "line2_station3", Name = "霞梧路口站", Order = 3, Lat = 24.575976, Lng = 118.097407, IsTerminal = false },
                new BusStationModel { Id = "line2_station4", Name = "叶厝站", Order = 4, Lat = 24.594314, Lng = 118.106702, IsTerminal = false },
                new BusStationModel { Id = "line2_station5", Name = "禹州大学城站", Order = 5, Lat = 24.621939, Lng = 118.127988, IsTerminal = false },
                new BusStationModel { Id = "line2_station6", Name = "洪塘头站", Order = 6, Lat = 24.627757, Lng = 118.131023, IsTerminal = false },
                new BusStationModel { Id = "line2_station7", Name = "酱文化园站", Order = 7, Lat = 24.649589, Lng = 118.143611, IsTerminal = false },
                new BusStationModel { Id = "line2_station8", Name = "内厝站", Order = 8, Lat = 24.665478, Lng = 118.273764, IsTerminal = false },
                new BusStationModel { Id = "line2_station9", Name = "厦华科技有限公司", Order = 9, Lat = 24.675507, Lng = 118.280386, IsTerminal = true }
            };
        }

        // 为其他车辆ID添加基础支持（可以复用2号线配置作为默认）
        if (carId == "1181" || carId == "1171" || carId == "5699" || carId == "1168")
        {
            // 暂时使用2号线的站点配置，但修改ID前缀
            var linePrefix = carId switch 
            {
                "1181" => "line1",
                "1171" => "line3", 
                "5699" => "line4",
                "1168" => "line5",
                _ => "line_unknown"
            };

            return new[]
            {
                new BusStationModel { Id = $"{linePrefix}_station0", Name = "总站(班车总站)", Order = 0, Lat = 0.0, Lng = 0.0, IsTerminal = true },
                new BusStationModel { Id = $"{linePrefix}_station1", Name = "嘉庚体育馆站", Order = 1, Lat = 24.587788, Lng = 118.106925, IsTerminal = false },
                new BusStationModel { Id = $"{linePrefix}_station2", Name = "集美旧厂区站", Order = 2, Lat = 24.596244, Lng = 118.097278, IsTerminal = false },
                new BusStationModel { Id = $"{linePrefix}_station3", Name = "霞梧路口站", Order = 3, Lat = 24.575976, Lng = 118.097407, IsTerminal = false },
                new BusStationModel { Id = $"{linePrefix}_station4", Name = "叶厝站", Order = 4, Lat = 24.594314, Lng = 118.106702, IsTerminal = false },
                new BusStationModel { Id = $"{linePrefix}_station5", Name = "禹州大学城站", Order = 5, Lat = 24.621939, Lng = 118.127988, IsTerminal = false },
                new BusStationModel { Id = $"{linePrefix}_station6", Name = "洪塘头站", Order = 6, Lat = 24.627757, Lng = 118.131023, IsTerminal = false },
                new BusStationModel { Id = $"{linePrefix}_station7", Name = "酱文化园站", Order = 7, Lat = 24.649589, Lng = 118.143611, IsTerminal = false },
                new BusStationModel { Id = $"{linePrefix}_station8", Name = "内厝站", Order = 8, Lat = 24.665478, Lng = 118.273764, IsTerminal = false },
                new BusStationModel { Id = $"{linePrefix}_station9", Name = "厦华科技有限公司", Order = 9, Lat = 24.675507, Lng = 118.280386, IsTerminal = true }
            };
        }
        
        // 未知车辆ID返回null，让前端处理
        return null;
    }

    /// <summary>
    /// 计算两点间距离（米）
    /// </summary>
    private double CalculateDistance(double lat1, double lng1, double lat2, double lng2)
    {
        const double R = 6371000; // 地球半径（米）
        var dLat = (lat2 - lat1) * Math.PI / 180;
        var dLng = (lng2 - lng1) * Math.PI / 180;
        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                Math.Cos(lat1 * Math.PI / 180) * Math.Cos(lat2 * Math.PI / 180) *
                Math.Sin(dLng / 2) * Math.Sin(dLng / 2);
        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        return R * c;
    }
}

/// <summary>
/// 响应第三方的车辆位置信息模型
/// </summary>
public class VehicleBusLocationInfo
{
    /// <summary>
    /// 是否编码
    /// </summary>
    [JsonPropertyName("encry")]
    public bool Encry { get; set; } = false;
    /// <summary>
    /// 车辆位置数据列表
    /// </summary>
    [JsonPropertyName("result")]
    public List<VehicleLocation> Result { get; set; } = new();
    /// <summary>
    /// 状态码
    /// </summary>
    [JsonPropertyName("status")]
    public int Status { get; set; }
}



/// <summary>
/// GPS API响应模型
/// </summary>
public class GpsApiResponse
{
    /// <summary>
    /// 响应状态码
    /// </summary>
    [JsonPropertyName("code")]
    public int Code { get; set; }

    /// <summary>
    /// 响应消息
    /// </summary>
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 车辆位置数据列表
    /// </summary>
    [JsonPropertyName("data")]
    public List<VehicleLocation> Data { get; set; } = new();

    /// <summary>
    /// 响应是否成功
    /// </summary>
    public bool IsSuccess => Code == 200 || Code == 0;
}

/// <summary>
/// GPS API请求模型
/// </summary>
public class GpsApiRequest
{
    /// <summary>
    /// 车辆ID列表
    /// </summary>
    public List<string> CarIds { get; set; } = new();
}

/// <summary>
/// 位置更新事件模型
/// </summary>
public class LocationUpdateEvent
{
    /// <summary>
    /// 事件类型
    /// </summary>
    public string EventType { get; set; } = "location_update";

    /// <summary>
    /// 更新的车辆位置列表
    /// </summary>
    public List<VehicleLocation> Locations { get; set; } = new();

    /// <summary>
    /// 事件时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 事件ID
    /// </summary>
    public string EventId { get; set; } = Guid.NewGuid().ToString();
}

/// <summary>
/// API响应基类
/// </summary>
public class ApiResponse<T>
{
    /// <summary>
    /// 是否成功
    /// </summary>
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    /// <summary>
    /// 响应消息
    /// </summary>
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 响应数据
    /// </summary>
    [JsonPropertyName("data")]
    public T? Data { get; set; }

    /// <summary>
    /// 时间戳
    /// </summary>
    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 创建成功响应
    /// </summary>
    public static ApiResponse<T> CreateSuccess(T data, string message = "操作成功")
    {
        return new ApiResponse<T>
        {
            Success = true,
            Message = message,
            Data = data
        };
    }

    /// <summary>
    /// 创建失败响应
    /// </summary>
    public static ApiResponse<T> CreateError(string message, T? data = default)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Message = message,
            Data = data
        };
    }
}

/// <summary>
/// 到站记录模型
/// </summary>
public class StationArrival
{
    /// <summary>
    /// 车辆ID
    /// </summary>
    public string CarId { get; set; } = string.Empty;

    /// <summary>
    /// 站点ID
    /// </summary>
    public string StationId { get; set; } = string.Empty;

    /// <summary>
    /// 站点名称
    /// </summary>
    public string StationName { get; set; } = string.Empty;

    /// <summary>
    /// 到站时间
    /// </summary>
    public DateTime ArrivalTime { get; set; }

    /// <summary>
    /// 到站时的车辆坐标
    /// </summary>
    public double Latitude { get; set; }
    
    /// <summary>
    /// 到站时的车辆坐标
    /// </summary>
    public double Longitude { get; set; }
}

/// <summary>
/// 路线计算请求模型
/// </summary>
public class RouteCalculationRequest
{
    /// <summary>
    /// 起点纬度
    /// </summary>
    public double FromLat { get; set; }

    /// <summary>
    /// 起点经度
    /// </summary>
    public double FromLng { get; set; }

    /// <summary>
    /// 终点纬度
    /// </summary>
    public double ToLat { get; set; }

    /// <summary>
    /// 终点经度
    /// </summary>
    public double ToLng { get; set; }

    /// <summary>
    /// 车辆ID
    /// </summary>
    public string CarId { get; set; } = string.Empty;

    /// <summary>
    /// 目标站点索引
    /// </summary>
    public int TargetStationIndex { get; set; }

    /// <summary>
    /// 当前车速（km/h）
    /// </summary>
    public double CurrentSpeed { get; set; } = 25;

    /// <summary>
    /// 是否从服务器获取到车速数据
    /// </summary>
    public bool HasServerSpeed { get; set; } = false;
}

/// <summary>
/// 路线计算结果模型
/// </summary>
public class RouteCalculationResult
{
    /// <summary>
    /// 距离（米）
    /// </summary>
    public int Distance { get; set; }

    /// <summary>
    /// 预估时间（分钟）
    /// </summary>
    public int EstimatedTime { get; set; }

    /// <summary>
    /// 站点数量
    /// </summary>
    public int StationCount { get; set; }

    /// <summary>
    /// 是否包含实时路况
    /// </summary>
    public bool HasTraffic { get; set; }

    /// <summary>
    /// 是否为兜底计算
    /// </summary>
    public bool IsBackup { get; set; }
}

/// <summary>
/// 腾讯地图API响应模型
/// </summary>
public class TencentMapResponse
{
    [JsonPropertyName("status")]
    public int Status { get; set; }

    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    [JsonPropertyName("result")]
    public TencentMapResult? Result { get; set; }
}

/// <summary>
/// 腾讯地图结果模型
/// </summary>
public class TencentMapResult
{
    [JsonPropertyName("routes")]
    public List<TencentMapRoute>? Routes { get; set; }
}

/// <summary>
/// 腾讯地图路线模型
/// </summary>
public class TencentMapRoute
{
    [JsonPropertyName("distance")]
    public double Distance { get; set; }

    [JsonPropertyName("duration")]
    public double Duration { get; set; }
}

/// <summary>
/// 公交站点模型
/// </summary>
public class BusStationModel
{
    /// <summary>
    /// 站点ID
    /// </summary>
    public string Id { get; set; } = string.Empty;
    
    /// <summary>
    /// 站点名称
    /// </summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// 站点顺序
    /// </summary>
    public int Order { get; set; }
    
    /// <summary>
    /// 纬度坐标
    /// </summary>
    public double Lat { get; set; }
    
    /// <summary>
    /// 经度坐标
    /// </summary>
    public double Lng { get; set; }
    
    /// <summary>
    /// 是否为终点站
    /// </summary>
    public bool IsTerminal { get; set; }
}

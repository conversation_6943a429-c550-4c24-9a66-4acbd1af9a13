// utils/request.js
const app = getApp()

/**
 * 封装的请求方法
 */
function request(options) {
  return new Promise((resolve, reject) => {
    // 显示加载提示
    if (options.showLoading !== false) {
      wx.showLoading({
        title: options.loadingText || '加载中...',
        mask: true
      })
    }

    // 构建完整的URL
    const baseUrl = app.globalData.baseUrl
    const url = options.url.startsWith('http') ? options.url : baseUrl + options.url

    // 构建请求头
    const header = {
      'Content-Type': 'application/json',
      ...options.header
    }

    // 添加授权头
    if (app.globalData.token) {
      header['Authorization'] = `Bearer ${app.globalData.token}`
    }

    wx.request({
      url: url,
      method: options.method || 'GET',
      data: options.data,
      header: header,
      success: (res) => {
        // 隐藏加载提示
        if (options.showLoading !== false) {
          wx.hideLoading()
        }

        // 处理HTTP状态码
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data)
        } else if (res.statusCode === 401) {
          // token过期，重新登录
          app.logout()
          reject(new Error('登录已过期，请重新登录'))
        } else {
          reject(new Error(`请求失败: ${res.statusCode}`))
        }
      },
      fail: (err) => {
        // 隐藏加载提示
        if (options.showLoading !== false) {
          wx.hideLoading()
        }

        console.error('请求失败:', err)
        
        // 显示错误提示
        if (options.showError !== false) {
          wx.showToast({
            title: '网络请求失败',
            icon: 'none'
          })
        }

        reject(err)
      }
    })
  })
}

/**
 * GET请求
 */
function get(url, data = {}, options = {}) {
  return request({
    url: url,
    method: 'GET',
    data: data,
    ...options
  })
}

/**
 * POST请求
 */
function post(url, data = {}, options = {}) {
  return request({
    url: url,
    method: 'POST',
    data: data,
    ...options
  })
}

/**
 * PUT请求
 */
function put(url, data = {}, options = {}) {
  return request({
    url: url,
    method: 'PUT',
    data: data,
    ...options
  })
}

/**
 * DELETE请求
 */
function del(url, data = {}, options = {}) {
  return request({
    url: url,
    method: 'DELETE',
    data: data,
    ...options
  })
}

module.exports = {
  request,
  get,
  post,
  put,
  delete: del
}

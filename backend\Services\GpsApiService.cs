using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Options;
using WeChatBus.Configuration;
using WeChatBus.Models;

namespace WeChatBus.Services;

/// <summary>
/// GPS API服务实现
/// </summary>
public class GpsApiService : IGpsApiService
{
    private readonly HttpClient _httpClient;
    private readonly GpsApiConfiguration _config;
    private readonly ILogger<GpsApiService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public GpsApiService(
        HttpClient httpClient,
        IOptions<GpsApiConfiguration> config,
        ILogger<GpsApiService> logger)
    {
        _httpClient = httpClient;
        _config = config.Value;
        _logger = logger;

        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            // 如果你的 time 字段不是标准 ISO 格式，需要自定义一下 DateTime 解析器
            // 这里因为格式是 "yyyy-MM-dd HH:mm:ss"，可以加一个自定义 Converter
        };

        // 配置HttpClient
        _httpClient.BaseAddress = new Uri(_config.BaseUrl);
        _httpClient.Timeout = TimeSpan.FromSeconds(_config.TimeoutSeconds);
        _httpClient.DefaultRequestHeaders.Add("Authorization", _config.Authorization);
    }

    /// <summary>
    /// 获取车辆位置信息
    /// </summary>
    public async Task<List<VehicleLocation>> GetVehicleLocationsAsync(List<string> carIds, CancellationToken cancellationToken = default)
    {
        if (carIds == null || !carIds.Any())
        {
            _logger.LogWarning("车辆ID列表为空");
            return new List<VehicleLocation>();
        }

        var retryCount = 0;
        while (retryCount <= _config.RetryCount)
        {
            try
            {
                var requestBody = JsonSerializer.Serialize(carIds, _jsonOptions);
                var content = new StringContent(requestBody, Encoding.UTF8, "application/json");

                var url = $"{_config.ApiPath}?addCarIds=''";
                var response = await _httpClient.PostAsync(url, content, cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

                    var apiResponse = JsonSerializer.Deserialize<VehicleBusLocationInfo>(responseContent, _jsonOptions);

                    if (apiResponse?.Status == 1 && apiResponse.Result != null)
                    {
                        // 更新数据时间戳
                        foreach (var location in apiResponse.Result)
                        {
                            location.UpdatedAt = DateTime.UtcNow;
                            location.ApplyMappingAndStatus();
                        }
                        apiResponse.Result = apiResponse.Result.OrderBy(l => l.CarLine).ToList();
                        _logger.LogInformation("成功获取车辆位置信息，数量: {Count}", apiResponse.Result.Count);
                        return apiResponse.Result;
                    }
                    else
                    {
                        _logger.LogWarning("GPS API返回失败响应");
                    }
                }
                else
                {
                    _logger.LogWarning("GPS API请求失败，状态码: {StatusCode}", response.StatusCode);
                }
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
            {
                _logger.LogWarning("GPS API请求超时，重试次数: {RetryCount}", retryCount);
            }
            catch (HttpRequestException ex)
            {
                _logger.LogWarning(ex, "GPS API网络请求异常，重试次数: {RetryCount}", retryCount);
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "GPS API响应JSON解析失败，重试次数: {RetryCount}", retryCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GPS API请求发生未知异常，重试次数: {RetryCount}", retryCount);
            }

            retryCount++;
            if (retryCount <= _config.RetryCount)
            {
                var delay = TimeSpan.FromMilliseconds(_config.RetryDelayMs * retryCount);
                await Task.Delay(delay, cancellationToken);
            }
        }

        _logger.LogError("GPS API请求失败，已达到最大重试次数: {MaxRetryCount}", _config.RetryCount);
        return new List<VehicleLocation>();
    }

    /// <summary>
    /// 获取所有配置的车辆位置信息
    /// </summary>
    public async Task<List<VehicleLocation>> GetAllVehicleLocationsAsync(CancellationToken cancellationToken = default)
    {
        return await GetVehicleLocationsAsync(_config.CarIds, cancellationToken);
    }

    /// <summary>
    /// 检查GPS API服务是否可用
    /// </summary>
    public async Task<bool> IsServiceAvailableAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // 使用一个车辆ID进行测试
            var testCarIds = _config.CarIds.Take(1).ToList();
            if (!testCarIds.Any())
            {
                return false;
            }

            var locations = await GetVehicleLocationsAsync(testCarIds, cancellationToken);
            return locations.Any();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查GPS API服务可用性时发生异常");
            return false;
        }
    }
}

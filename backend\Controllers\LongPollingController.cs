using Microsoft.AspNetCore.Mvc;
using System.Text.Json;
using WeChatBus.Models;
using WeChatBus.Services;

namespace WeChatBus.Controllers;

/// <summary>
/// 长轮询控制器
/// 提供HTTP长轮询接口用于实时获取车辆位置更新
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class LongPollingController : ControllerBase
{
    private readonly EventBus _eventBus;
    private readonly IRedisService _redisService;
    private readonly ILogger<LongPollingController> _logger;
    private static readonly TimeSpan Timeout = TimeSpan.FromSeconds(60); // 增加默认超时到60秒
    private readonly JsonSerializerOptions _jsonOptions;

    public LongPollingController(
        EventBus eventBus,
        IRedisService redisService,
        ILogger<LongPollingController> logger)
    {
        _eventBus = eventBus;
        _redisService = redisService;
        _logger = logger;

        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    /// <summary>
    /// 长轮询接口 - 等待车辆位置更新
    /// </summary>
    /// <param name="timeout">超时时间（秒），默认30秒</param>
    /// <param name="ct">取消令牌</param>
    /// <returns>位置更新数据或超时响应</returns>
    [HttpGet("poll")]
    public async Task<IActionResult> Poll([FromQuery] int timeout = 25, CancellationToken ct = default) // 默认25秒
    {
        // 限制超时时间范围
        timeout = Math.Max(5, Math.Min(timeout, 60));
        var actualTimeout = TimeSpan.FromSeconds(timeout);

        var tcs = new TaskCompletionSource<string>(TaskCreationOptions.RunContinuationsAsynchronously);
        var channel = "wechat_bus:location_update";

        _logger.LogDebug("开始长轮询请求，超时时间: {Timeout}秒", timeout);

        Guid subscriptionId = Guid.Empty;
        // 订阅事件，保存订阅ID
        subscriptionId = _eventBus.Subscribe(channel, (callback) =>
        {
            _logger.LogDebug("长轮询收到数据，准备返回");
            // 收到数据后立即取消订阅，避免回调泄漏
            _eventBus.Unsubscribe(channel, subscriptionId);
            tcs.TrySetResult(callback);
        });

        // 链接外部取消或超时
        using var cts = CancellationTokenSource.CreateLinkedTokenSource(ct);
        cts.CancelAfter(actualTimeout);

        try
        {
            var result = await tcs.Task.WaitAsync(cts.Token);

            // 解析位置更新事件
            var updateEvent = JsonSerializer.Deserialize<LocationUpdateEvent>(result, _jsonOptions);

            _logger.LogDebug("长轮询返回数据，车辆数量: {Count}", updateEvent?.Locations?.Count ?? 0);

            return Ok(ApiResponse<LocationUpdateEvent>.CreateSuccess(updateEvent, "获取位置更新成功"));
        }
        catch (OperationCanceledException)
        {
            // 超时或客户端取消时，也需要取消订阅
            _eventBus.Unsubscribe(channel, subscriptionId);
            _logger.LogDebug("长轮询超时，返回空结果");

            // 返回当前缓存的位置数据作为兜底
            try
            {
                var cachedLocations = await _redisService.GetAllVehicleLocationsAsync();
                if (cachedLocations.Any())
                {
                    var fallbackEvent = new LocationUpdateEvent
                    {
                        Locations = cachedLocations,
                        Timestamp = DateTime.UtcNow,
                        EventId = Guid.NewGuid().ToString()
                    };
                    return Ok(ApiResponse<LocationUpdateEvent>.CreateSuccess(fallbackEvent, "获取缓存位置数据"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "获取缓存位置数据失败");
            }

            // 返回204 No Content表示超时
            return NoContent();
        }
        catch (Exception ex)
        {
            // 确保取消订阅
            _eventBus.Unsubscribe(channel, subscriptionId);
            _logger.LogError(ex, "长轮询处理异常");
            return StatusCode(500, ApiResponse<LocationUpdateEvent>.CreateError("长轮询处理异常"));
        }
    }


    /// <summary>
    /// 获取EventBus状态信息
    /// </summary>
    [HttpGet("poll/status")]
    public IActionResult GetPollStatus()
    {
        try
        {
            var channels = _eventBus.GetAllChannels();
            var status = new
            {
                Channels = channels,
                TotalSubscribers = channels.Values.Sum(),
                Timestamp = DateTime.UtcNow
            };

            return Ok(ApiResponse<object>.CreateSuccess(status, "获取长轮询状态成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取长轮询状态失败");
            return StatusCode(500, ApiResponse<object>.CreateError("获取长轮询状态失败"));
        }
    }

    /// <summary>
    /// 手动触发位置更新事件（用于测试）
    /// </summary>
    [HttpPost("poll/trigger")]
    public async Task<IActionResult> TriggerUpdate()
    {
        try
        {
            _logger.LogInformation("手动触发位置更新事件");

            // 获取当前位置数据
            var locations = await _redisService.GetAllVehicleLocationsAsync();

            var updateEvent = new LocationUpdateEvent
            {
                Locations = locations,
                Timestamp = DateTime.UtcNow,
                EventId = Guid.NewGuid().ToString()
            };

            // 发布到Redis
            await _redisService.PublishLocationUpdateAsync(updateEvent);

            return Ok(ApiResponse<object>.CreateSuccess(
                new { EventId = updateEvent.EventId, LocationCount = locations.Count },
                "手动触发更新成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "手动触发位置更新失败");
            return StatusCode(500, ApiResponse<object>.CreateError("手动触发更新失败"));
        }
    }

    /// <summary>
    /// 清理EventBus中的空频道
    /// </summary>
    [HttpPost("poll/cleanup")]
    public IActionResult CleanupChannels()
    {
        try
        {
            _logger.LogInformation("手动清理EventBus空频道");
            _eventBus.CleanupEmptyChannels();

            var channels = _eventBus.GetAllChannels();
            return Ok(ApiResponse<object>.CreateSuccess(
                new { RemainingChannels = channels.Count, Channels = channels },
                "清理空频道成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理空频道失败");
            return StatusCode(500, ApiResponse<object>.CreateError("清理空频道失败"));
        }
    }
}

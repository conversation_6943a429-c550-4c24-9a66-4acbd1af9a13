using Microsoft.AspNetCore.Mvc;
using WeChatBus.Models;
using WeChatBus.Services;
using WeChatBus.Services.BackgroundServices;

namespace WeChatBus.Controllers;

/// <summary>
/// 车辆位置控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class VehicleController : ControllerBase
{
    private readonly IGpsApiService _gpsApiService;
    private readonly IRedisService _redisService;
    private readonly ILogger<VehicleController> _logger;

    public VehicleController(
        IGpsApiService gpsApiService,
        IRedisService redisService,
        ILogger<VehicleController> logger)
    {
        _gpsApiService = gpsApiService;
        _redisService = redisService;
        _logger = logger;
    }

    /// <summary>
    /// 获取所有车辆位置信息
    /// </summary>
    [HttpGet("locations")]
    public async Task<ActionResult<ApiResponse<List<VehicleLocation>>>> GetAllLocations()
    {
        try
        {
            // 首先尝试从Redis获取缓存数据
            var cachedLocations = await _redisService.GetAllVehicleLocationsAsync();
            
            if (cachedLocations.Any())
            {
                return Ok(ApiResponse<List<VehicleLocation>>.CreateSuccess(cachedLocations, "获取车辆位置成功"));
            }

            // 如果缓存为空，直接从GPS API获取
            var locations = await _gpsApiService.GetAllVehicleLocationsAsync();
            
            if (locations.Any())
            {
                // 保存到Redis缓存
                await _redisService.SaveVehicleLocationsAsync(locations);
            }

            return Ok(ApiResponse<List<VehicleLocation>>.CreateSuccess(locations, "获取车辆位置成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取车辆位置信息时发生异常");
            return StatusCode(500, ApiResponse<List<VehicleLocation>>.CreateError("获取车辆位置失败"));
        }
    }

    /// <summary>
    /// 获取指定车辆位置信息
    /// </summary>
    [HttpGet("locations/{carId}")]
    public async Task<ActionResult<ApiResponse<VehicleLocation>>> GetVehicleLocation(string carId)
    {
        try
        {
            if (string.IsNullOrEmpty(carId))
            {
                return BadRequest(ApiResponse<VehicleLocation>.CreateError("车辆ID不能为空"));
            }

            // 首先尝试从Redis获取
            var location = await _redisService.GetVehicleLocationAsync(carId);
            
            if (location != null)
            {
                return Ok(ApiResponse<VehicleLocation>.CreateSuccess(location, "获取车辆位置成功"));
            }

            // 如果Redis中没有，从GPS API获取
            var locations = await _gpsApiService.GetVehicleLocationsAsync(new List<string> { carId });
            var vehicleLocation = locations.FirstOrDefault();

            if (vehicleLocation != null)
            {
                // 保存到Redis
                await _redisService.SaveVehicleLocationsAsync(new List<VehicleLocation> { vehicleLocation });
                return Ok(ApiResponse<VehicleLocation>.CreateSuccess(vehicleLocation, "获取车辆位置成功"));
            }

            return NotFound(ApiResponse<VehicleLocation>.CreateError("未找到指定车辆的位置信息"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取车辆位置信息时发生异常: {CarId}", carId);
            return StatusCode(500, ApiResponse<VehicleLocation>.CreateError("获取车辆位置失败"));
        }
    }

    /// <summary>
    /// 模拟车辆位置更新（仅用于测试）
    /// </summary>
    [HttpPost("simulate-update")]
    public async Task<IActionResult> SimulateLocationUpdate([FromBody] VehicleLocation simulatedLocation)
    {
        try
        {
            if (simulatedLocation == null)
            {
                return BadRequest(new ApiResponse<object>
                {
                    Success = false,
                    Message = "模拟位置数据不能为空"
                });
            }

            // 应用映射和状态计算，传递Redis服务用于保存到站记录
            simulatedLocation.ApplyMappingAndStatus(_redisService);

            // 保存到Redis
            await _redisService.SaveVehicleLocationAsync(simulatedLocation);

            // 发布位置更新事件
            var locationUpdateEvent = new LocationUpdateEvent
            {
                Locations = new List<VehicleLocation> { simulatedLocation },
                Timestamp = DateTime.UtcNow,
                EventId = Guid.NewGuid().ToString()
            };

            await _redisService.PublishLocationUpdateAsync(locationUpdateEvent);

            _logger.LogInformation("模拟位置更新成功: 车辆{CarId} 位置({Lat}, {Lng})", 
                simulatedLocation.CarId, simulatedLocation.Latitude, simulatedLocation.Longitude);

            return Ok(new ApiResponse<VehicleLocation>
            {
                Success = true,
                Message = "模拟位置更新成功",
                Data = simulatedLocation
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "模拟位置更新失败");
            return StatusCode(500, new ApiResponse<object>
            {
                Success = false,
                Message = "模拟位置更新失败: " + ex.Message
            });
        }
    }


    /// <summary>
    /// 获取服务状态
    /// </summary>
    [HttpGet("status")]
    public async Task<ActionResult<ApiResponse<object>>> GetServiceStatus()
    {
        try
        {
            var redisConnected = await _redisService.IsConnectedAsync();
            var gpsApiAvailable = await _gpsApiService.IsServiceAvailableAsync();

            var status = new
            {
                RedisConnected = redisConnected,
                GpsApiAvailable = gpsApiAvailable,
                Timestamp = DateTime.UtcNow
            };

            return Ok(ApiResponse<object>.CreateSuccess(status, "获取服务状态成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取服务状态时发生异常");
            return StatusCode(500, ApiResponse<object>.CreateError("获取服务状态失败"));
        }
    }

    /// <summary>
    /// 测试GPS API连接
    /// </summary>
    [HttpGet("test-gps")]
    public async Task<ActionResult<ApiResponse<object>>> TestGpsApi()
    {
        try
        {
            _logger.LogInformation("测试GPS API连接");
            
            var isAvailable = await _gpsApiService.IsServiceAvailableAsync();
            var locations = await _gpsApiService.GetAllVehicleLocationsAsync();

            var result = new
            {
                IsAvailable = isAvailable,
                LocationCount = locations.Count,
                Locations = locations.Take(3), // 只返回前3个位置用于测试
                Timestamp = DateTime.UtcNow
            };

            return Ok(ApiResponse<object>.CreateSuccess(result, "GPS API测试完成"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "测试GPS API时发生异常");
            return StatusCode(500, ApiResponse<object>.CreateError("GPS API测试失败"));
        }
    }

    /// <summary>
    /// 清理过期数据
    /// </summary>
    [HttpPost("cleanup")]
    public async Task<ActionResult<ApiResponse<string>>> CleanupData()
    {
        try
        {
            _logger.LogInformation("清理过期数据");
            await _redisService.CleanupExpiredDataAsync();
            return Ok(ApiResponse<string>.CreateSuccess("清理完成", "过期数据清理成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期数据时发生异常");
            return StatusCode(500, ApiResponse<string>.CreateError("清理数据失败"));
        }
    }

    /// <summary>
    /// 计算路线信息（替代前端腾讯地图直接调用）
    /// </summary>
    [HttpPost("calculate-route")]
    public async Task<ActionResult<ApiResponse<RouteCalculationResult>>> CalculateRoute([FromBody] RouteCalculationRequest request)
    {
        try
        {
            if (request == null || request.FromLat == 0 || request.FromLng == 0 || request.ToLat == 0 || request.ToLng == 0)
            {
                return BadRequest(ApiResponse<RouteCalculationResult>.CreateError("路线计算参数不完整"));
            }

            _logger.LogInformation("开始计算路线: 从({FromLat}, {FromLng}) 到({ToLat}, {ToLng})", 
                request.FromLat, request.FromLng, request.ToLat, request.ToLng);

            // 调用腾讯地图API或兜底计算
            var result = await calculateRouteInternal(request);

            return Ok(ApiResponse<RouteCalculationResult>.CreateSuccess(result, "路线计算成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "计算路线时发生异常");
            return StatusCode(500, ApiResponse<RouteCalculationResult>.CreateError("路线计算失败"));
        }
    }

    /// <summary>
    /// 内部路线计算逻辑
    /// </summary>
    private async Task<RouteCalculationResult> calculateRouteInternal(RouteCalculationRequest request)
    {
        try
        {
            // 尝试使用腾讯地图API
            var tencentResult = await callTencentMapAPI(request);
            if (tencentResult != null)
            {
                return tencentResult;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "腾讯地图API调用失败，使用兜底计算");
        }

        // 兜底：使用简化计算
        return CalculateFallbackRoute(request);
    }

    /// <summary>
    /// 调用腾讯地图API
    /// </summary>
    private async Task<RouteCalculationResult?> callTencentMapAPI(RouteCalculationRequest request)
    {
        using var httpClient = new HttpClient();
        
        var key = "UATBZ-URB3M-LLD6G-65XEC-TRAK7-KFBA2";
        var sk = "eh0Ni9sNcY8MaXOzCdQpNTdrIlWtgexH";
        
        // 计算车速
        var actualSpeed = request.HasServerSpeed && request.CurrentSpeed > 0 
            ? Math.Max(5, Math.Min(120, request.CurrentSpeed)) 
            : 25;
        var speedMs = Math.Max(1.4, (actualSpeed * 1000) / 3600);

        var parameters = new Dictionary<string, string>
        {
            ["from"] = $"{request.FromLat},{request.FromLng}",
            ["to"] = $"{request.ToLat},{request.ToLng}",
            ["key"] = key,
            ["output"] = "json",
            ["policy"] = "LEAST_TIME",
            ["speed"] = speedMs.ToString("F6"),
            ["get_mp"] = "1"
        };

        // 计算签名
        var path = "/ws/direction/v1/driving/";
        var sig = ComputeTencentMapSignature(path, parameters, sk);
        
        var queryString = string.Join("&", parameters.Select(p => $"{p.Key}={Uri.EscapeDataString(p.Value)}"));
        var url = $"https://apis.map.qq.com{path}?{queryString}&sig={sig}";

        var response = await httpClient.GetAsync(url);
        if (response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsStringAsync();
            var apiResult = System.Text.Json.JsonSerializer.Deserialize<TencentMapResponse>(content);
            
            if (apiResult?.Status == 0 && apiResult.Result?.Routes?.Any() == true)
            {
                var route = apiResult.Result.Routes.First();
                var distance = route.Distance;
                var duration = route.Duration + Math.Max(1, distance / 20000); // 添加余量

                return new RouteCalculationResult
                {
                    Distance = (int)Math.Round(distance),
                    EstimatedTime = (int)Math.Round(duration),
                    StationCount = Math.Max(1, request.TargetStationIndex),
                    HasTraffic = true,
                    IsBackup = false
                };
            }
        }

        return null;
    }

    /// <summary>
    /// 腾讯地图签名计算
    /// </summary>
    private string ComputeTencentMapSignature(string path, Dictionary<string, string> parameters, string secretKey)
    {
        var sortedParams = parameters.OrderBy(p => p.Key);
        var queryString = string.Join("&", sortedParams.Select(p => $"{p.Key}={p.Value}"));
        var rawString = $"{path}?{queryString}{secretKey}";
        var encodedString = Uri.EscapeUriString(rawString);
        
        using var md5 = System.Security.Cryptography.MD5.Create();
        var hash = md5.ComputeHash(System.Text.Encoding.UTF8.GetBytes(encodedString));
        return Convert.ToHexString(hash).ToLower();
    }

    /// <summary>
    /// 兜底路线计算
    /// </summary>
    private RouteCalculationResult CalculateFallbackRoute(RouteCalculationRequest request)
    {
        // 计算直线距离
        var distance = CalculateDistance(request.FromLat, request.FromLng, request.ToLat, request.ToLng);
        var roadDistance = distance * 1.4; // 道路弯曲系数
        
        // 计算预估时间
        var avgSpeed = 18.0; // km/h
        var travelTimeMinutes = (roadDistance / 1000) / avgSpeed * 60;
        var cityDelayMinutes = Math.Max(2, roadDistance / 1000 * 0.75); // 每公里45秒延误
        var totalTimeMinutes = travelTimeMinutes + cityDelayMinutes;
        
        // 加上站点停靠时间
        var stationCount = Math.Max(1, request.TargetStationIndex);
        var stopTimeMinutes = stationCount * 0.5; // 每站30秒
        
        _logger.LogDebug("兜底计算结果: 距离{Distance}米, 行驶{TravelTime}分钟, 延误{CityDelay}分钟, 停靠{StopTime}分钟, 总计{TotalTime}分钟",
            roadDistance, travelTimeMinutes, cityDelayMinutes, stopTimeMinutes, totalTimeMinutes + stopTimeMinutes);

        return new RouteCalculationResult
        {
            Distance = (int)Math.Round(roadDistance),
            EstimatedTime = (int)Math.Round(totalTimeMinutes + stopTimeMinutes),
            StationCount = stationCount,
            HasTraffic = false,
            IsBackup = true
        };
    }

    /// <summary>
    /// 计算两点间距离（米）
    /// </summary>
    private double CalculateDistance(double lat1, double lng1, double lat2, double lng2)
    {
        const double R = 6371000; // 地球半径（米）
        var dLat = (lat2 - lat1) * Math.PI / 180;
        var dLng = (lng2 - lng1) * Math.PI / 180;
        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                Math.Cos(lat1 * Math.PI / 180) * Math.Cos(lat2 * Math.PI / 180) *
                Math.Sin(dLng / 2) * Math.Sin(dLng / 2);
        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        return R * c;
    }
}

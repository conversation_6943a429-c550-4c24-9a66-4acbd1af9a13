using StackExchange.Redis;
using WeChatBus.Configuration;
using WeChatBus.Services;
using WeChatBus.Services.BackgroundServices;

var builder = WebApplication.CreateBuilder(args);

// 添加配置
builder.Services.Configure<GpsApiConfiguration>(
    builder.Configuration.GetSection(GpsApiConfiguration.SectionName));
builder.Services.Configure<DataSyncConfiguration>(
    builder.Configuration.GetSection(DataSyncConfiguration.SectionName));
builder.Services.Configure<RedisConfiguration>(
    builder.Configuration.GetSection(RedisConfiguration.SectionName));

// 添加控制器
builder.Services.AddControllers();

// 配置Redis连接
builder.Services.AddSingleton<IConnectionMultiplexer>(provider =>
{
    var connectionString = builder.Configuration.GetConnectionString("Redis") ?? "localhost:6379";
    return ConnectionMultiplexer.Connect(connectionString);
});

// 注册服务
builder.Services.AddSingleton<IRedisService, RedisService>();
builder.Services.AddSingleton<IGpsApiService, GpsApiService>();
builder.Services.AddSingleton<EventBus>();

// 注册后台服务
builder.Services.AddHostedService<BusDataSyncService>();
builder.Services.AddHostedService<RedisSubscriberService>();

// 配置HTTP客户端
builder.Services.AddHttpClient<IGpsApiService, GpsApiService>();

// 配置CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});

// 配置Swagger
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { Title = "WeChatBus API", Version = "v1" });
});

var app = builder.Build();

// 配置请求管道
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

app.UseCors("AllowAll");

// 启用静态文件服务
app.UseStaticFiles();

app.MapControllers();

// 健康检查端点
app.MapGet("/health", async (IRedisService redisService, IGpsApiService gpsApiService) =>
{
    var redisConnected = await redisService.IsConnectedAsync();
    var gpsApiAvailable = await gpsApiService.IsServiceAvailableAsync();

    var health = new
    {
        Status = redisConnected && gpsApiAvailable ? "Healthy" : "Unhealthy",
        Redis = redisConnected,
        GpsApi = gpsApiAvailable,
        Timestamp = DateTime.UtcNow
    };

    return Results.Ok(health);
});

app.Run();

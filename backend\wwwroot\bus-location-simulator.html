<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>班车位置模拟器</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .control-group {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        .control-group h3 {
            margin-top: 0;
            color: #007bff;
        }

        .form-row {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 15px;
        }

        label {
            min-width: 80px;
            font-weight: 600;
            color: #495057;
        }

        select, input, button {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }

        select, input {
            flex: 1;
        }

        button {
            background: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
            font-weight: 600;
        }

        button:hover {
            background: #0056b3;
        }

        .btn-success {
            background: #28a745;
        }

        .btn-success:hover {
            background: #1e7e34;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background: #e0a800;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .stations {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .station-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s;
            cursor: pointer;
        }

        .station-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .station-card.current {
            border-color: #28a745;
            background: #d4edda;
        }

        .station-card.next {
            border-color: #ffc107;
            background: #fff3cd;
        }

        .station-card.passed {
            border-color: #6c757d;
            background: #f8f9fa;
            opacity: 0.7;
        }

        .station-name {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .station-info {
            font-size: 12px;
            color: #666;
        }

        .status-panel {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .status-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }

        .status-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .status-value {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .logs {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }

        .logs .timestamp {
            color: #68d391;
        }

        .logs .info {
            color: #63b3ed;
        }

        .logs .error {
            color: #fc8181;
        }

        .logs .warning {
            color: #f6e05e;
        }

        .logs .debug {
            color: #a0aec0;
            font-size: 12px;
        }

        .quick-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .quick-actions button {
            flex: 1;
            min-width: 150px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚌 班车位置模拟器</h1>
        
        <div class="controls">
            <div class="control-group">
                <h3>车辆设置</h3>
                <div class="form-row">
                    <label>车辆ID:</label>
                    <select id="carId">
                        <option value="1162">2号线 (1162)</option>
                        <option value="1181">1号线 (1181)</option>
                        <option value="1171">3号线 (1171)</option>
                        <option value="5699">4号线 (5699)</option>
                        <option value="1168">5号线 (1168)</option>
                    </select>
                </div>
                <div class="form-row">
                    <label>速度:</label>
                    <input type="number" id="speed" value="25" min="0" max="60">
                    <span>km/h</span>
                </div>
            </div>

            <div class="control-group">
                <h3>位置设置</h3>
                <div class="form-row">
                    <label>纬度:</label>
                    <input type="number" id="latitude" value="24.587788" step="0.000001">
                </div>
                <div class="form-row">
                    <label>经度:</label>
                    <input type="number" id="longitude" value="118.106925" step="0.000001">
                </div>
            </div>
        </div>

        <div class="quick-actions">
            <button onclick="moveToStation(1)" class="btn-success">移动到嘉庚体育馆</button>
            <button onclick="moveToStation(2)" class="btn-success">移动到集美旧厂区</button>
            <button onclick="moveToStation(9)" class="btn-success">移动到终点站</button>
            <button onclick="moveToTerminal()" class="btn-warning">移动到总站</button>
            <button onclick="simulateMoving()" class="btn-warning">模拟行驶中</button>
            <button onclick="startAutoTest()" class="btn-warning">自动测试</button>
            <button onclick="clearLogs()" class="btn-danger">清空日志</button>
        </div>

        <div class="status-panel">
            <h3>当前状态</h3>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-label">详细地址</div>
                    <div class="status-value" id="detailAddress">-</div>
                </div>
                <div class="status-item">
                    <div class="status-label">当前站点</div>
                    <div class="status-value" id="currentStation">-</div>
                </div>
                <div class="status-item">
                    <div class="status-label">下一站</div>
                    <div class="status-value" id="nextStation">-</div>
                </div>
                <div class="status-item">
                    <div class="status-label">到站状态</div>
                    <div class="status-value" id="arrivalStatus">未到站</div>
                </div>
                <div class="status-item">
                    <div class="status-label">预估信息</div>
                    <div class="status-value" id="estimateInfo">-</div>
                </div>
                <div class="status-item">
                    <div class="status-label">车辆状态</div>
                    <div class="status-value" id="vehicleState">在线</div>
                </div>
                <div class="status-item">
                    <div class="status-label">最后更新</div>
                    <div class="status-value" id="lastUpdate">-</div>
                </div>
            </div>
        </div>

        <h3>2号线站点</h3>
        <div class="stations" id="stations">
            <!-- 站点将通过JavaScript动态生成 -->
        </div>

        <div style="margin-bottom: 20px;">
            <button onclick="sendLocationUpdate()" class="btn-success" style="width: 48%; padding: 15px; font-size: 16px; margin-right: 2%;">
                📡 发送位置更新到服务器
            </button>
            <button onclick="testLongPolling()" class="btn-warning" style="width: 48%; padding: 15px; font-size: 16px;">
                🔄 测试长轮询接口
            </button>
        </div>

        <div style="margin-bottom: 20px;">
            <button onclick="testAllAPIs()" class="btn-success" style="width: 48%; padding: 10px; font-size: 14px; margin-right: 2%;">
                🧪 测试所有API接口
            </button>
            <button onclick="testRedisConnection()" class="btn-warning" style="width: 48%; padding: 10px; font-size: 14px;">
                🔌 测试Redis连接
            </button>
        </div>

        <h3>操作日志</h3>
        <div class="logs" id="logs">
            <div class="timestamp">[启动]</div> 班车位置模拟器已启动<br>
        </div>
    </div>

    <script>
        // 2号线站点配置
        const stations = [
            { id: "line2_station0", name: "总站(班车总站)", order: 0, lat: 0, lng: 0, isTerminal: true },
            { id: "line2_station1", name: "嘉庚体育馆站", order: 1, lat: 24.587788, lng: 118.106925, isTerminal: false },
            { id: "line2_station2", name: "集美旧厂区站", order: 2, lat: 24.596244, lng: 118.097278, isTerminal: false },
            { id: "line2_station3", name: "霞梧路口站", order: 3, lat: 24.575976, lng: 118.097407, isTerminal: false },
            { id: "line2_station4", name: "叶厝站", order: 4, lat: 24.594314, lng: 118.106702, isTerminal: false },
            { id: "line2_station5", name: "禹州大学城站", order: 5, lat: 24.621939, lng: 118.127988, isTerminal: false },
            { id: "line2_station6", name: "洪塘头站", order: 6, lat: 24.627757, lng: 118.131023, isTerminal: false },
            { id: "line2_station7", name: "酱文化园站", order: 7, lat: 24.649589, lng: 118.143611, isTerminal: false },
            { id: "line2_station8", name: "内厝站", order: 8, lat: 24.665478, lng: 118.273764, isTerminal: false },
            { id: "line2_station9", name: "厦华科技有限公司", order: 9, lat: 24.675507, lng: 118.280386, isTerminal: true }
        ];

        let autoTestRunning = false;
        let realTimeUpdateInterval = null;

        // 地址数据库（模拟真实地址）
        const addressDatabase = {
            '24.580000,118.100000': '福建省厦门市集美区班车总站停车场',
            '24.587788,118.106925': '福建省厦门市集美区嘉庚路199号嘉庚体育馆',
            '24.596244,118.097278': '福建省厦门市集美区杏林湾路集美旧厂区',
            '24.575976,118.097407': '福建省厦门市集美区霞梧路与杏林北路交叉口',
            '24.594314,118.106702': '福建省厦门市集美区叶厝村委会附近',
            '24.621939,118.127988': '福建省厦门市集美区禹州大学城商业街',
            '24.627757,118.131023': '福建省厦门市集美区洪塘头村洪塘大道',
            '24.649589,118.143611': '福建省厦门市集美区酱文化园园区内',
            '24.665478,118.273764': '福建省厦门市集美区内厝镇政府附近',
            '24.675507,118.280386': '福建省厦门市集美区厦华科技有限公司园区'
        };

        // 获取详细地址
        function getDetailedAddress(lat, lng) {
            const key = `${lat.toFixed(6)},${lng.toFixed(6)}`;
            const exactMatch = addressDatabase[key];
            if (exactMatch) return exactMatch;

            // 模糊匹配最近的地址
            let minDistance = Infinity;
            let closestAddress = `福建省厦门市集美区坐标(${lat.toFixed(4)}, ${lng.toFixed(4)})附近`;

            for (const [coordKey, address] of Object.entries(addressDatabase)) {
                const [dbLat, dbLng] = coordKey.split(',').map(Number);
                const distance = Math.sqrt(Math.pow(lat - dbLat, 2) + Math.pow(lng - dbLng, 2));
                if (distance < minDistance) {
                    minDistance = distance;
                    if (distance < 0.001) { // 很近的距离
                        closestAddress = address;
                    }
                }
            }

            return closestAddress;
        }

        // 计算预估信息
        function calculateEstimate(currentIndex, targetIndex) {
            if (currentIndex === -1 || targetIndex === -1 || currentIndex >= targetIndex) {
                return { stations: 0, time: 0, distance: 0, text: '到站' };
            }

            const stationCount = targetIndex - currentIndex;
            const baseTime = stationCount * 3; // 每站3分钟基础时间
            const randomTime = Math.floor(Math.random() * stationCount * 2); // 随机延迟
            const estimatedTime = baseTime + randomTime;
            
            const baseDistance = stationCount * 1.2; // 每站1.2公里基础距离
            const randomDistance = Math.random() * stationCount * 0.5;
            const estimatedDistance = baseDistance + randomDistance;

            const distanceText = estimatedDistance >= 1 ? 
                `${estimatedDistance.toFixed(1)}公里` : 
                `${Math.round(estimatedDistance * 1000)}米`;

            return {
                stations: stationCount,
                time: estimatedTime,
                distance: estimatedDistance,
                text: `${stationCount}站 / ${estimatedTime}分钟 / ${distanceText}`
            };
        }

        // 初始化页面
        function init() {
            renderStations(); // 显示默认站点布局
            updateStatus(); // 显示默认状态
            log('info', '页面初始化完成 - 所有状态由后端计算');
        }

        // 渲染站点（基于后端数据）
        function renderStations(currentStationIndex = -1, passedStationIds = [], isAtStation = false) {
            const stationsContainer = document.getElementById('stations');
            stationsContainer.innerHTML = '';

            stations.forEach((station, index) => {
                const stationCard = document.createElement('div');
                stationCard.className = 'station-card';
                stationCard.onclick = () => moveToStation(index);

                let status = 'waiting';
                if (passedStationIds.includes(station.id)) {
                    status = 'passed';
                } else if (currentStationIndex === index) {
                    status = 'current';
                }

                stationCard.className += ` ${status}`;

                stationCard.innerHTML = `
                    <div class="station-name">${station.name}</div>
                    <div class="station-info">
                        站点 ${index + 1}<br>
                        ${station.lat !== 0 ? `${station.lat.toFixed(6)}, ${station.lng.toFixed(6)}` : '总站'}
                    </div>
                `;

                stationsContainer.appendChild(stationCard);
            });
        }

        // 移动到指定站点（仅更新坐标，让后端计算状态）
        function moveToStation(stationIndex) {
            if (stationIndex < 0 || stationIndex >= stations.length) return;

            const station = stations[stationIndex];
            if (station.lat === 0 && station.lng === 0) {
                // 总站特殊处理
                document.getElementById('latitude').value = 24.580000;
                document.getElementById('longitude').value = 118.100000;
            } else {
                document.getElementById('latitude').value = station.lat;
                document.getElementById('longitude').value = station.lng;
            }

            log('info', `📍 手动移动到 ${station.name}`);
            log('info', `坐标: (${document.getElementById('latitude').value}, ${document.getElementById('longitude').value})`);
        }

        // 移动到总站（仅更新坐标）
        function moveToTerminal() {
            document.getElementById('latitude').value = 24.580000;
            document.getElementById('longitude').value = 118.100000;
            log('info', '📍 手动移动到总站');
        }

        // 模拟行驶中（简单的随机移动）
        function simulateMoving() {
            const currentLat = parseFloat(document.getElementById('latitude').value);
            const currentLng = parseFloat(document.getElementById('longitude').value);

            const newLat = currentLat + (Math.random() - 0.5) * 0.002;
            const newLng = currentLng + (Math.random() - 0.5) * 0.002;

            document.getElementById('latitude').value = newLat.toFixed(6);
            document.getElementById('longitude').value = newLng.toFixed(6);

            log('info', `🚗 随机移动: (${newLat.toFixed(6)}, ${newLng.toFixed(6)})`);
        }

        // 更新状态显示（完全基于后端数据）
        function updateStatus(backendData = null) {
            const currentLat = parseFloat(document.getElementById('latitude').value);
            const currentLng = parseFloat(document.getElementById('longitude').value);
            
            // 更新详细地址
            const detailAddress = getDetailedAddress(currentLat, currentLng);
            document.getElementById('detailAddress').textContent = detailAddress;
            
            // 如果有后端数据，使用后端数据；否则显示默认值
            if (backendData) {
                document.getElementById('currentStation').textContent = backendData.currentStation || '计算中';
                document.getElementById('nextStation').textContent = backendData.nextStation || '计算中';
                document.getElementById('arrivalStatus').textContent = backendData.isAtStation ? '已到站' : '行驶中';
                
                // 计算预估信息
                if (backendData.isAtStation) {
                    document.getElementById('estimateInfo').textContent = '到站';
                } else if (backendData.currentStationIndex !== undefined && backendData.currentStationIndex !== -1) {
                    const nextIndex = backendData.currentStationIndex + 1;
                    if (nextIndex < stations.length) {
                        const estimate = calculateEstimate(backendData.currentStationIndex, nextIndex);
                        document.getElementById('estimateInfo').textContent = estimate.text;
                    } else {
                        document.getElementById('estimateInfo').textContent = '终点站';
                    }
                } else {
                    document.getElementById('estimateInfo').textContent = '计算中...';
                }
            } else {
                // 没有后端数据时的默认显示
                document.getElementById('currentStation').textContent = '获取中...';
                document.getElementById('nextStation').textContent = '获取中...';
                document.getElementById('arrivalStatus').textContent = '获取中...';
                document.getElementById('estimateInfo').textContent = '计算中...';
            }
            
            document.getElementById('vehicleState').textContent = '在线';
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
        }

        // 发送位置更新到服务器（简化版）
        async function sendLocationUpdate() {
            const carId = document.getElementById('carId').value;
            const latitude = parseFloat(document.getElementById('latitude').value);
            const longitude = parseFloat(document.getElementById('longitude').value);
            const speed = parseInt(document.getElementById('speed').value);

            const locationData = {
                carId: carId,
                lat: latitude,
                lng: longitude,
                latitude: latitude,  
                longitude: longitude,
                addr: getDetailedAddress(latitude, longitude), 
                speed: speed,
                time: new Date().toISOString(),
                state: 10, 
                isOnline: true
            };

            try {
                const response = await fetch('/api/vehicle/simulate-update', {
                    method: 'POST',
                    headers: { 
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(locationData)
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    log('success', `✅ 位置数据发送成功: 车辆${carId}`);
                    log('info', `📍 坐标: (${latitude.toFixed(6)}, ${longitude.toFixed(6)})`);
                    log('info', `🏠 地址: ${locationData.addr}`);
                    
                    // 使用后端返回的数据更新状态显示
                    if (result.data) {
                        const backendData = result.data;
                        log('info', `🔄 后端计算: ${backendData.currentStation || '未知'} → ${backendData.nextStation || '未知'}`);
                        log('info', `📊 状态: ${backendData.isAtStation ? '已到站' : '行驶中'}`);
                        
                        // 更新显示（完全基于后端数据）
                        updateStatus(backendData);
                        
                        // 更新站点可视化
                        if (backendData.passedStationIds) {
                            try {
                                const backendPassedStations = JSON.parse(backendData.passedStationIds);
                                log('info', `📋 已过站点: ${backendPassedStations.length}个`);
                                renderStations(backendData.currentStationIndex, backendPassedStations, backendData.isAtStation);
                            } catch (e) {
                                log('debug', '站点数据解析异常');
                            }
                        }
                    }
                } else {
                    log('error', `❌ 发送失败: ${result.message || '未知错误'}`);
                }
                
            } catch (error) {
                log('error', `❌ 网络错误: ${error.message}`);
                log('info', '💡 请确保后端服务正在运行');
            }
        }

        // 测试长轮询接口
        async function testLongPolling() {
            try {
                log('info', '🔄 开始测试长轮询接口...');
                
                const response = await fetch('/api/longpolling/poll?timeout=5', {
                    method: 'GET',
                    headers: { 'Accept': 'application/json' }
                });

                if (response.status === 200) {
                    const result = await response.json();
                    if (result.success && result.data) {
                        log('success', `✅ 长轮询获取到数据: ${result.data.length}个车辆位置`);
                        result.data.forEach((location, index) => {
                            log('info', `📍 车辆${location.carId}: ${location.currentStation || '未知位置'} -> ${location.nextStation || '未知'}`);
                        });
                    } else {
                        log('info', '📡 长轮询正常，但暂无新数据');
                    }
                } else if (response.status === 204) {
                    log('info', '⏰ 长轮询超时，无新数据');
                } else {
                    log('error', `❌ 长轮询请求失败: ${response.status}`);
                }
            } catch (error) {
                log('error', `❌ 长轮询测试失败: ${error.message}`);
            }
        }

        // 测试所有API接口
        async function testAllAPIs() {
            log('info', '🧪 开始API接口全面测试...');

            // 1. 测试服务状态
            try {
                const statusResponse = await fetch('/api/vehicle/status');
                const statusResult = await statusResponse.json();
                if (statusResponse.ok) {
                    log('success', '✅ 服务状态检查通过');
                    log('info', `📊 Redis连接: ${statusResult.data?.redisConnected ? '正常' : '异常'}`);
                } else {
                    log('error', '❌ 服务状态检查失败');
                }
            } catch (error) {
                log('error', `❌ 服务状态测试失败: ${error.message}`);
            }

            // 2. 测试获取所有车辆位置
            try {
                const locationsResponse = await fetch('/api/vehicle/locations');
                const locationsResult = await locationsResponse.json();
                if (locationsResponse.ok && locationsResult.success) {
                    log('success', `✅ 获取车辆位置成功: ${locationsResult.data.length}个车辆`);
                    locationsResult.data.forEach(location => {
                        log('info', `🚌 车辆${location.carId}: ${location.currentStation} (在线: ${location.isOnline})`);
                    });
                } else {
                    log('error', '❌ 获取车辆位置失败');
                }
            } catch (error) {
                log('error', `❌ 车辆位置测试失败: ${error.message}`);
            }

            // 3. 测试获取单个车辆位置
            try {
                const carId = document.getElementById('carId').value;
                const singleResponse = await fetch(`/api/vehicle/location/${carId}`);
                const singleResult = await singleResponse.json();
                if (singleResponse.ok && singleResult.success) {
                    log('success', `✅ 获取车辆${carId}位置成功`);
                    log('info', `📍 位置: ${singleResult.data.currentStation} -> ${singleResult.data.nextStation}`);
                } else {
                    log('error', `❌ 获取车辆${carId}位置失败`);
                }
            } catch (error) {
                log('error', `❌ 单车辆位置测试失败: ${error.message}`);
            }

            // 4. 测试模拟位置更新
            await sendLocationUpdate();

            log('info', '🎉 API接口测试完成！');
        }

        // 测试Redis连接
        async function testRedisConnection() {
            try {
                log('info', '🔌 开始测试Redis连接...');
                
                const response = await fetch('/api/vehicle/status');
                const result = await response.json();
                
                if (response.ok && result.success) {
                    const isRedisConnected = result.data?.redisConnected;
                    if (isRedisConnected) {
                        log('success', '✅ Redis连接正常');
                        log('info', '📊 可以正常保存和读取车辆位置数据');
                    } else {
                        log('error', '❌ Redis连接异常');
                        log('error', '💡 请检查Redis服务是否启动，配置是否正确');
                    }
                    
                    // 显示其他服务状态
                    if (result.data) {
                        log('info', `🕐 服务器时间: ${new Date(result.data.serverTime).toLocaleString()}`);
                        log('info', `🔄 GPS API状态: ${result.data.gpsApiStatus || '未知'}`);
                    }
                } else {
                    log('error', '❌ 无法获取服务状态');
                }
            } catch (error) {
                log('error', `❌ Redis连接测试失败: ${error.message}`);
                log('info', '💡 请确保后端服务正在运行');
            }
        }

        // 自动测试（简化版，只做GPS移动，让后端处理所有逻辑）
        function startAutoTest() {
            if (autoTestRunning) {
                // 停止自动测试
                clearInterval(realTimeUpdateInterval);
                autoTestRunning = false;
                document.querySelector('button[onclick="startAutoTest()"]').textContent = '自动测试';
                document.querySelector('button[onclick="startAutoTest()"]').className = 'btn-warning';
                log('info', '⏹️ 自动测试已停止');
                return;
            }

            // 开始自动测试
            autoTestRunning = true;
            document.querySelector('button[onclick="startAutoTest()"]').textContent = '停止测试';
            document.querySelector('button[onclick="startAutoTest()"]').className = 'btn-danger';
            log('info', '▶️ 开始自动测试 - 每5秒发送位置数据，完全依赖后端逻辑');

            // 每5秒执行一次更新
            realTimeUpdateInterval = setInterval(async () => {
                // 1. 简单的GPS移动
                simulateRealisticMovement();
                
                // 2. 发送位置到服务器，让后端计算所有状态
                await sendLocationUpdate();
                
            }, 5000); // 5秒间隔
        }

        // 简化的GPS移动（只负责位置移动，不管理状态）
        function simulateRealisticMovement() {
            const currentLat = parseFloat(document.getElementById('latitude').value);
            const currentLng = parseFloat(document.getElementById('longitude').value);

            // 简单的随机移动，让后端来判断状态
            const newLat = currentLat + (Math.random() - 0.5) * 0.001;
            const newLng = currentLng + (Math.random() - 0.5) * 0.001;
            
            document.getElementById('latitude').value = newLat.toFixed(6);
            document.getElementById('longitude').value = newLng.toFixed(6);
            
            log('info', `🚗 GPS更新: (${newLat.toFixed(6)}, ${newLng.toFixed(6)})`);
        }

        // 移除不需要的站点转换函数
        function simulateStationTransition() {
            // 不需要了，让后端处理
        }

        // 清空日志
        function clearLogs() {
            document.getElementById('logs').innerHTML = 
                '<div class="timestamp">[清空]</div> 日志已清空<br>';
        }

        // 添加日志
        function log(type, message) {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `<div class="timestamp">[${timestamp}]</div> <span class="${type}">${message}</span><br>`;
            logs.innerHTML += logEntry;
            logs.scrollTop = logs.scrollHeight;
        }

        // 页面加载完成后初始化
        window.onload = init;

        // 自动发送心跳
        setInterval(() => {
            log('info', `心跳: 车辆${document.getElementById('carId').value} 状态正常`);
        }, 30000);
    </script>
</body>
</html>
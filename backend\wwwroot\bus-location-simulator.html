<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>班车位置模拟器</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .control-group {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        .control-group h3 {
            margin-top: 0;
            color: #007bff;
        }

        .form-row {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 15px;
        }

        label {
            min-width: 80px;
            font-weight: 600;
            color: #495057;
        }

        select, input, button {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }

        select, input {
            flex: 1;
        }

        button {
            background: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
            font-weight: 600;
        }

        button:hover {
            background: #0056b3;
        }

        .btn-success {
            background: #28a745;
        }

        .btn-success:hover {
            background: #1e7e34;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background: #e0a800;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .stations {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .station-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s;
            cursor: pointer;
        }

        .station-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .station-card.current {
            border-color: #28a745;
            background: #d4edda;
        }

        .station-card.next {
            border-color: #ffc107;
            background: #fff3cd;
        }

        .station-card.passed {
            border-color: #6c757d;
            background: #f8f9fa;
            opacity: 0.7;
        }

        .station-name {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .station-info {
            font-size: 12px;
            color: #666;
        }

        .status-panel {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .status-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }

        .status-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .status-value {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .logs {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }

        .logs .timestamp {
            color: #68d391;
        }

        .logs .info {
            color: #63b3ed;
        }

        .logs .error {
            color: #fc8181;
        }

        .logs .warning {
            color: #f6e05e;
        }

        .logs .debug {
            color: #a0aec0;
            font-size: 12px;
        }

        .quick-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .quick-actions button {
            flex: 1;
            min-width: 150px;
        }

        .auto-test-progress {
            background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }

        .test-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }

        .stat-item {
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            text-align: center;
            border-left: 3px solid #007bff;
        }

        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚌 班车位置模拟器</h1>
        
        <div class="controls">
            <div class="control-group">
                <h3>车辆设置</h3>
                <div class="form-row">
                    <label>车辆ID:</label>
                    <select id="carId">
                        <option value="1162">2号线 (1162)</option>
                        <option value="1181">1号线 (1181)</option>
                        <option value="1171">3号线 (1171)</option>
                        <option value="5699">4号线 (5699)</option>
                        <option value="1168">5号线 (1168)</option>
                    </select>
                </div>
                <div class="form-row">
                    <label>速度:</label>
                    <input type="number" id="speed" value="25" min="0" max="60">
                    <span>km/h</span>
                </div>
            </div>

            <div class="control-group">
                <h3>位置设置</h3>
                <div class="form-row">
                    <label>纬度:</label>
                    <input type="number" id="latitude" value="24.587788" step="0.000001">
                </div>
                <div class="form-row">
                    <label>经度:</label>
                    <input type="number" id="longitude" value="118.106925" step="0.000001">
                </div>
            </div>
        </div>

        <div class="quick-actions">
            <button onclick="moveToStation(1)" class="btn-success">移动到嘉庚体育馆</button>
            <button onclick="moveToStation(2)" class="btn-success">移动到集美旧厂区</button>
            <button onclick="moveToStation(9)" class="btn-success">移动到终点站</button>
            <button onclick="moveToTerminal()" class="btn-warning">移动到总站</button>
            <button onclick="simulateMoving()" class="btn-warning">模拟行驶中</button>
            <button onclick="startAutoTest()" class="btn-warning">自动测试</button>
            <button onclick="generateTestReport()" class="btn-success">生成报告</button>
            <button onclick="exportTestData()" class="btn-success">导出数据</button>
            <button onclick="clearLogs()" class="btn-danger">清空日志</button>
        </div>

        <div class="status-panel">
            <h3>当前状态</h3>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-label">详细地址</div>
                    <div class="status-value" id="detailAddress">-</div>
                </div>
                <div class="status-item">
                    <div class="status-label">当前站点</div>
                    <div class="status-value" id="currentStation">-</div>
                </div>
                <div class="status-item">
                    <div class="status-label">下一站</div>
                    <div class="status-value" id="nextStation">-</div>
                </div>
                <div class="status-item">
                    <div class="status-label">到站状态</div>
                    <div class="status-value" id="arrivalStatus">未到站</div>
                </div>
                <div class="status-item">
                    <div class="status-label">预估信息</div>
                    <div class="status-value" id="estimateInfo">-</div>
                </div>
                <div class="status-item">
                    <div class="status-label">车辆状态</div>
                    <div class="status-value" id="vehicleState">在线</div>
                </div>
                <div class="status-item">
                    <div class="status-label">最后更新</div>
                    <div class="status-value" id="lastUpdate">-</div>
                </div>
                <div class="status-item">
                    <div class="status-label">自动测试进度</div>
                    <div class="status-value" id="autoTestProgress">未启动</div>
                </div>
                <div class="status-item">
                    <div class="status-label">测试统计</div>
                    <div class="status-value" id="testStats">-</div>
                </div>
            </div>
        </div>

        <h3>2号线站点</h3>
        <div class="stations" id="stations">
            <!-- 站点将通过JavaScript动态生成 -->
        </div>

        <div style="margin-bottom: 20px;">
            <button onclick="sendLocationUpdate()" class="btn-success" style="width: 48%; padding: 15px; font-size: 16px; margin-right: 2%;">
                📡 发送位置更新到服务器
            </button>
            <button onclick="testLongPolling()" class="btn-warning" style="width: 48%; padding: 15px; font-size: 16px;">
                🔄 测试长轮询接口
            </button>
        </div>

        <div style="margin-bottom: 20px;">
            <button onclick="testAllAPIs()" class="btn-success" style="width: 32%; padding: 10px; font-size: 14px; margin-right: 1%;">
                🧪 测试所有API接口
            </button>
            <button onclick="testRedisConnection()" class="btn-warning" style="width: 32%; padding: 10px; font-size: 14px; margin-right: 1%;">
                🔌 测试Redis连接
            </button>
            <button onclick="testConcurrentUsers()" class="btn-success" style="width: 32%; padding: 10px; font-size: 14px;">
                👥 多用户并发测试
            </button>
        </div>

        <h3>操作日志</h3>
        <div class="logs" id="logs">
            <div class="timestamp">[启动]</div> 班车位置模拟器已启动<br>
        </div>
    </div>

    <script>
        // 2号线站点配置
        const stations = [
            { id: "line2_station0", name: "总站(班车总站)", order: 0, lat: 0, lng: 0, isTerminal: true },
            { id: "line2_station1", name: "嘉庚体育馆站", order: 1, lat: 24.587788, lng: 118.106925, isTerminal: false },
            { id: "line2_station2", name: "集美旧厂区站", order: 2, lat: 24.596244, lng: 118.097278, isTerminal: false },
            { id: "line2_station3", name: "霞梧路口站", order: 3, lat: 24.575976, lng: 118.097407, isTerminal: false },
            { id: "line2_station4", name: "叶厝站", order: 4, lat: 24.594314, lng: 118.106702, isTerminal: false },
            { id: "line2_station5", name: "禹州大学城站", order: 5, lat: 24.621939, lng: 118.127988, isTerminal: false },
            { id: "line2_station6", name: "洪塘头站", order: 6, lat: 24.627757, lng: 118.131023, isTerminal: false },
            { id: "line2_station7", name: "酱文化园站", order: 7, lat: 24.649589, lng: 118.143611, isTerminal: false },
            { id: "line2_station8", name: "内厝站", order: 8, lat: 24.665478, lng: 118.273764, isTerminal: false },
            { id: "line2_station9", name: "厦华科技有限公司", order: 9, lat: 24.675507, lng: 118.280386, isTerminal: true }
        ];

        let autoTestRunning = false;
        let realTimeUpdateInterval = null;
        let currentAutoStationIndex = 0; // 当前自动测试的站点索引
        let autoTestCycleCount = 0; // 自动测试循环次数
        let autoTestStartTime = null; // 自动测试开始时间
        let autoTestStats = { // 自动测试统计
            totalUpdates: 0,
            successfulUpdates: 0,
            failedUpdates: 0,
            stationsVisited: 0,
            cyclesCompleted: 0
        };

        // 地址数据库（模拟真实地址）
        const addressDatabase = {
            '24.580000,118.100000': '福建省厦门市集美区班车总站停车场',
            '24.587788,118.106925': '福建省厦门市集美区嘉庚路199号嘉庚体育馆',
            '24.596244,118.097278': '福建省厦门市集美区杏林湾路集美旧厂区',
            '24.575976,118.097407': '福建省厦门市集美区霞梧路与杏林北路交叉口',
            '24.594314,118.106702': '福建省厦门市集美区叶厝村委会附近',
            '24.621939,118.127988': '福建省厦门市集美区禹州大学城商业街',
            '24.627757,118.131023': '福建省厦门市集美区洪塘头村洪塘大道',
            '24.649589,118.143611': '福建省厦门市集美区酱文化园园区内',
            '24.665478,118.273764': '福建省厦门市集美区内厝镇政府附近',
            '24.675507,118.280386': '福建省厦门市集美区厦华科技有限公司园区'
        };

        // 获取详细地址
        function getDetailedAddress(lat, lng) {
            const key = `${lat.toFixed(6)},${lng.toFixed(6)}`;
            const exactMatch = addressDatabase[key];
            if (exactMatch) return exactMatch;

            // 模糊匹配最近的地址
            let minDistance = Infinity;
            let closestAddress = `福建省厦门市集美区坐标(${lat.toFixed(4)}, ${lng.toFixed(4)})附近`;

            for (const [coordKey, address] of Object.entries(addressDatabase)) {
                const [dbLat, dbLng] = coordKey.split(',').map(Number);
                const distance = Math.sqrt(Math.pow(lat - dbLat, 2) + Math.pow(lng - dbLng, 2));
                if (distance < minDistance) {
                    minDistance = distance;
                    if (distance < 0.001) { // 很近的距离
                        closestAddress = address;
                    }
                }
            }

            return closestAddress;
        }

        // 计算预估信息
        function calculateEstimate(currentIndex, targetIndex) {
            if (currentIndex === -1 || targetIndex === -1 || currentIndex >= targetIndex) {
                return { stations: 0, time: 0, distance: 0, text: '到站' };
            }

            const stationCount = targetIndex - currentIndex;
            const baseTime = stationCount * 3; // 每站3分钟基础时间
            const randomTime = Math.floor(Math.random() * stationCount * 2); // 随机延迟
            const estimatedTime = baseTime + randomTime;
            
            const baseDistance = stationCount * 1.2; // 每站1.2公里基础距离
            const randomDistance = Math.random() * stationCount * 0.5;
            const estimatedDistance = baseDistance + randomDistance;

            const distanceText = estimatedDistance >= 1 ? 
                `${estimatedDistance.toFixed(1)}公里` : 
                `${Math.round(estimatedDistance * 1000)}米`;

            return {
                stations: stationCount,
                time: estimatedTime,
                distance: estimatedDistance,
                text: `${stationCount}站 / ${estimatedTime}分钟 / ${distanceText}`
            };
        }

        // 初始化页面
        function init() {
            renderStations(); // 显示默认站点布局
            updateStatus(); // 显示默认状态
            log('info', '页面初始化完成 - 所有状态由后端计算');
        }

        // 渲染站点（基于后端数据）
        function renderStations(currentStationIndex = -1, passedStationIds = [], isAtStation = false) {
            const stationsContainer = document.getElementById('stations');
            stationsContainer.innerHTML = '';

            stations.forEach((station, index) => {
                const stationCard = document.createElement('div');
                stationCard.className = 'station-card';
                stationCard.onclick = () => moveToStation(index);

                let status = 'waiting';
                if (passedStationIds.includes(station.id)) {
                    status = 'passed';
                } else if (currentStationIndex === index) {
                    status = 'current';
                }

                stationCard.className += ` ${status}`;

                stationCard.innerHTML = `
                    <div class="station-name">${station.name}</div>
                    <div class="station-info">
                        站点 ${index + 1}<br>
                        ${station.lat !== 0 ? `${station.lat.toFixed(6)}, ${station.lng.toFixed(6)}` : '总站'}
                    </div>
                `;

                stationsContainer.appendChild(stationCard);
            });
        }

        // 移动到指定站点（仅更新坐标，让后端计算状态）
        function moveToStation(stationIndex) {
            if (stationIndex < 0 || stationIndex >= stations.length) return;

            const station = stations[stationIndex];
            if (station.lat === 0 && station.lng === 0) {
                // 总站特殊处理
                document.getElementById('latitude').value = 24.580000;
                document.getElementById('longitude').value = 118.100000;
            } else {
                document.getElementById('latitude').value = station.lat;
                document.getElementById('longitude').value = station.lng;
            }

            log('info', `📍 手动移动到 ${station.name}`);
            log('info', `坐标: (${document.getElementById('latitude').value}, ${document.getElementById('longitude').value})`);
        }

        // 移动到总站（仅更新坐标）
        function moveToTerminal() {
            document.getElementById('latitude').value = 24.580000;
            document.getElementById('longitude').value = 118.100000;
            log('info', '📍 手动移动到总站');
        }

        // 模拟行驶中（简单的随机移动）
        function simulateMoving() {
            const currentLat = parseFloat(document.getElementById('latitude').value);
            const currentLng = parseFloat(document.getElementById('longitude').value);

            const newLat = currentLat + (Math.random() - 0.5) * 0.002;
            const newLng = currentLng + (Math.random() - 0.5) * 0.002;

            document.getElementById('latitude').value = newLat.toFixed(6);
            document.getElementById('longitude').value = newLng.toFixed(6);

            log('info', `🚗 随机移动: (${newLat.toFixed(6)}, ${newLng.toFixed(6)})`);
        }

        // 更新状态显示（完全基于后端数据）
        function updateStatus(backendData = null) {
            const currentLat = parseFloat(document.getElementById('latitude').value);
            const currentLng = parseFloat(document.getElementById('longitude').value);

            // 更新详细地址
            const detailAddress = getDetailedAddress(currentLat, currentLng);
            document.getElementById('detailAddress').textContent = detailAddress;

            // 如果有后端数据，使用后端数据；否则显示默认值
            if (backendData) {
                document.getElementById('currentStation').textContent = backendData.currentStation || '计算中';
                document.getElementById('nextStation').textContent = backendData.nextStation || '计算中';
                document.getElementById('arrivalStatus').textContent = backendData.isAtStation ? '已到站' : '行驶中';

                // 计算预估信息
                if (backendData.isAtStation) {
                    document.getElementById('estimateInfo').textContent = '到站';
                } else if (backendData.currentStationIndex !== undefined && backendData.currentStationIndex !== -1) {
                    const nextIndex = backendData.currentStationIndex + 1;
                    if (nextIndex < stations.length) {
                        const estimate = calculateEstimate(backendData.currentStationIndex, nextIndex);
                        document.getElementById('estimateInfo').textContent = estimate.text;
                    } else {
                        document.getElementById('estimateInfo').textContent = '终点站';
                    }
                } else {
                    document.getElementById('estimateInfo').textContent = '计算中...';
                }

                // 自动测试模式下的额外验证
                if (autoTestRunning) {
                    validateAutoTestResults(backendData);
                }
            } else {
                // 没有后端数据时的默认显示
                document.getElementById('currentStation').textContent = '获取中...';
                document.getElementById('nextStation').textContent = '获取中...';
                document.getElementById('arrivalStatus').textContent = '获取中...';
                document.getElementById('estimateInfo').textContent = '计算中...';
            }

            document.getElementById('vehicleState').textContent = '在线';
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();

            // 更新自动测试进度
            updateAutoTestProgress();
        }

        // 验证自动测试结果
        function validateAutoTestResults(backendData) {
            const currentStation = stations[currentAutoStationIndex];
            if (!currentStation) return;

            // 验证50米到站检测
            if (backendData.isAtStation) {
                log('success', `✅ 50米到站检测成功: ${backendData.currentStation}`);

                // 验证已过站点记录
                if (backendData.passedStationIds) {
                    try {
                        const passedIds = JSON.parse(backendData.passedStationIds);
                        log('info', `📋 已过站点更新: ${passedIds.length}个站点`);

                        // 验证站点顺序是否正确
                        const expectedPassedCount = Math.max(0, currentAutoStationIndex);
                        if (passedIds.length >= expectedPassedCount) {
                            log('success', '✅ 已过站点记录验证通过');
                        } else {
                            log('warning', `⚠️ 已过站点数量异常: 期望${expectedPassedCount}, 实际${passedIds.length}`);
                        }
                    } catch (e) {
                        log('error', '❌ 已过站点数据解析失败');
                    }
                }
            } else {
                log('info', `🚗 行驶中: ${backendData.currentStation} → ${backendData.nextStation}`);
            }

            // 验证Redis缓存更新
            if (backendData.currentStationIndex !== undefined) {
                log('debug', `🔄 Redis状态更新: 站点索引${backendData.currentStationIndex}`);
            }
        }

        // 发送位置更新到服务器（简化版）
        async function sendLocationUpdate() {
            const carId = document.getElementById('carId').value;
            const latitude = parseFloat(document.getElementById('latitude').value);
            const longitude = parseFloat(document.getElementById('longitude').value);
            const speed = parseInt(document.getElementById('speed').value);

            const locationData = {
                carId: carId,
                lat: latitude,
                lng: longitude,
                latitude: latitude,  
                longitude: longitude,
                addr: getDetailedAddress(latitude, longitude), 
                speed: speed,
                time: new Date().toISOString(),
                state: 10, 
                isOnline: true
            };

            try {
                const response = await fetch('/api/vehicle/simulate-update', {
                    method: 'POST',
                    headers: { 
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(locationData)
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    log('success', `✅ 位置数据发送成功: 车辆${carId}`);
                    log('info', `📍 坐标: (${latitude.toFixed(6)}, ${longitude.toFixed(6)})`);
                    log('info', `🏠 地址: ${locationData.addr}`);
                    
                    // 使用后端返回的数据更新状态显示
                    if (result.data) {
                        const backendData = result.data;
                        log('info', `🔄 后端计算: ${backendData.currentStation || '未知'} → ${backendData.nextStation || '未知'}`);
                        log('info', `📊 状态: ${backendData.isAtStation ? '已到站' : '行驶中'}`);
                        
                        // 更新显示（完全基于后端数据）
                        updateStatus(backendData);
                        
                        // 更新站点可视化
                        if (backendData.passedStationIds) {
                            try {
                                const backendPassedStations = JSON.parse(backendData.passedStationIds);
                                log('info', `📋 已过站点: ${backendPassedStations.length}个`);
                                renderStations(backendData.currentStationIndex, backendPassedStations, backendData.isAtStation);
                            } catch (e) {
                                log('debug', '站点数据解析异常');
                            }
                        }
                    }
                } else {
                    log('error', `❌ 发送失败: ${result.message || '未知错误'}`);
                }
                
            } catch (error) {
                log('error', `❌ 网络错误: ${error.message}`);
                log('info', '💡 请确保后端服务正在运行');
            }
        }

        // 测试长轮询接口（增强版）
        async function testLongPolling() {
            try {
                log('info', '🔄 开始测试长轮询接口...');
                const startTime = Date.now();

                const response = await fetch('/api/longpolling/poll?timeout=5', {
                    method: 'GET',
                    headers: { 'Accept': 'application/json' }
                });

                const responseTime = Date.now() - startTime;

                if (response.status === 200) {
                    const result = await response.json();
                    if (result.success && result.data) {
                        log('success', `✅ 长轮询获取到数据: ${result.data.locations ? result.data.locations.length : 'N/A'}个车辆位置 (响应时间: ${responseTime}ms)`);

                        // 验证数据结构
                        const locations = result.data.locations || [];
                        locations.forEach((location, index) => {
                            log('info', `📍 车辆${location.carId}: ${location.currentStation || '未知位置'} -> ${location.nextStation || '未知'}`);

                            // 验证关键字段
                            if (location.passedStationIds) {
                                try {
                                    const passedIds = JSON.parse(location.passedStationIds);
                                    log('debug', `   已过站点: ${passedIds.length}个`);
                                } catch (e) {
                                    log('warning', '   已过站点数据格式异常');
                                }
                            }
                        });

                        // 验证事件ID和时间戳
                        if (result.data.eventId) {
                            log('debug', `📡 事件ID: ${result.data.eventId}`);
                        }
                        if (result.data.timestamp) {
                            log('debug', `⏰ 事件时间: ${new Date(result.data.timestamp).toLocaleTimeString()}`);
                        }
                    } else {
                        log('info', '📡 长轮询正常，但暂无新数据');
                    }
                } else if (response.status === 204) {
                    log('info', `⏰ 长轮询超时，无新数据 (等待时间: ${responseTime}ms)`);
                } else {
                    log('error', `❌ 长轮询请求失败: ${response.status}`);
                }
            } catch (error) {
                log('error', `❌ 长轮询测试失败: ${error.message}`);
            }
        }

        // 测试所有API接口
        async function testAllAPIs() {
            log('info', '🧪 开始API接口全面测试...');

            // 1. 测试服务状态
            try {
                const statusResponse = await fetch('/api/vehicle/status');
                const statusResult = await statusResponse.json();
                if (statusResponse.ok) {
                    log('success', '✅ 服务状态检查通过');
                    log('info', `📊 Redis连接: ${statusResult.data?.redisConnected ? '正常' : '异常'}`);
                } else {
                    log('error', '❌ 服务状态检查失败');
                }
            } catch (error) {
                log('error', `❌ 服务状态测试失败: ${error.message}`);
            }

            // 2. 测试获取所有车辆位置
            try {
                const locationsResponse = await fetch('/api/vehicle/locations');
                const locationsResult = await locationsResponse.json();
                if (locationsResponse.ok && locationsResult.success) {
                    log('success', `✅ 获取车辆位置成功: ${locationsResult.data.length}个车辆`);
                    locationsResult.data.forEach(location => {
                        log('info', `🚌 车辆${location.carId}: ${location.currentStation} (在线: ${location.isOnline})`);
                    });
                } else {
                    log('error', '❌ 获取车辆位置失败');
                }
            } catch (error) {
                log('error', `❌ 车辆位置测试失败: ${error.message}`);
            }

            // 3. 测试获取单个车辆位置
            try {
                const carId = document.getElementById('carId').value;
                const singleResponse = await fetch(`/api/vehicle/location/${carId}`);
                const singleResult = await singleResponse.json();
                if (singleResponse.ok && singleResult.success) {
                    log('success', `✅ 获取车辆${carId}位置成功`);
                    log('info', `📍 位置: ${singleResult.data.currentStation} -> ${singleResult.data.nextStation}`);
                } else {
                    log('error', `❌ 获取车辆${carId}位置失败`);
                }
            } catch (error) {
                log('error', `❌ 单车辆位置测试失败: ${error.message}`);
            }

            // 4. 测试模拟位置更新
            await sendLocationUpdate();

            log('info', '🎉 API接口测试完成！');
        }

        // 测试Redis连接
        async function testRedisConnection() {
            try {
                log('info', '🔌 开始测试Redis连接...');
                
                const response = await fetch('/api/vehicle/status');
                const result = await response.json();
                
                if (response.ok && result.success) {
                    const isRedisConnected = result.data?.redisConnected;
                    if (isRedisConnected) {
                        log('success', '✅ Redis连接正常');
                        log('info', '📊 可以正常保存和读取车辆位置数据');
                    } else {
                        log('error', '❌ Redis连接异常');
                        log('error', '💡 请检查Redis服务是否启动，配置是否正确');
                    }
                    
                    // 显示其他服务状态
                    if (result.data) {
                        log('info', `🕐 服务器时间: ${new Date(result.data.serverTime).toLocaleString()}`);
                        log('info', `🔄 GPS API状态: ${result.data.gpsApiStatus || '未知'}`);
                    }
                } else {
                    log('error', '❌ 无法获取服务状态');
                }
            } catch (error) {
                log('error', `❌ Redis连接测试失败: ${error.message}`);
                log('info', '💡 请确保后端服务正在运行');
            }
        }

        // 智能自动测试（站点循环功能）
        function startAutoTest() {
            if (autoTestRunning) {
                // 停止自动测试
                clearInterval(realTimeUpdateInterval);
                autoTestRunning = false;
                document.querySelector('button[onclick="startAutoTest()"]').textContent = '自动测试';
                document.querySelector('button[onclick="startAutoTest()"]').className = 'btn-warning';

                // 显示测试总结
                const duration = autoTestStartTime ? Math.round((Date.now() - autoTestStartTime) / 1000) : 0;
                log('info', `⏹️ 自动测试已停止 - 运行时长: ${duration}秒`);
                log('info', `📊 测试统计: 成功${autoTestStats.successfulUpdates}/${autoTestStats.totalUpdates}次更新, 访问${autoTestStats.stationsVisited}个站点, 完成${autoTestStats.cyclesCompleted}个循环`);

                // 重置状态
                resetAutoTestStats();
                updateAutoTestProgress();
                return;
            }

            // 开始智能自动测试
            autoTestRunning = true;
            autoTestStartTime = Date.now();
            currentAutoStationIndex = 0; // 从总站开始
            resetAutoTestStats();

            document.querySelector('button[onclick="startAutoTest()"]').textContent = '停止测试';
            document.querySelector('button[onclick="startAutoTest()"]').className = 'btn-danger';
            log('info', '🚀 开始智能自动测试 - 站点循环模式');
            log('info', '📋 测试计划: 按顺序访问所有站点，触发50米到站检测，验证后端业务逻辑');

            // 立即执行第一次移动
            executeAutoTestStep();

            // 每5秒执行一次自动测试步骤
            realTimeUpdateInterval = setInterval(executeAutoTestStep, 5000);
        }

        // 执行自动测试步骤（智能站点循环）
        async function executeAutoTestStep() {
            if (!autoTestRunning) return;

            try {
                // 获取当前目标站点
                const targetStation = stations[currentAutoStationIndex];
                if (!targetStation) {
                    log('error', '❌ 无效的站点索引，重置到总站');
                    currentAutoStationIndex = 0;
                    return;
                }

                log('info', `🎯 自动测试步骤 ${autoTestStats.totalUpdates + 1}: 前往 ${targetStation.name}`);

                // 智能移动到目标站点（模拟真实移动轨迹）
                await moveToStationIntelligently(currentAutoStationIndex);

                // 发送位置更新到服务器
                autoTestStats.totalUpdates++;
                const success = await sendLocationUpdateWithStats();

                if (success) {
                    autoTestStats.successfulUpdates++;
                    autoTestStats.stationsVisited++;

                    // 移动到下一个站点
                    currentAutoStationIndex++;

                    // 如果到达终点，重新开始循环
                    if (currentAutoStationIndex >= stations.length) {
                        currentAutoStationIndex = 0;
                        autoTestCycleCount++;
                        autoTestStats.cyclesCompleted++;
                        log('success', `🔄 完成第 ${autoTestCycleCount} 个完整循环，重新从总站开始`);
                    }
                } else {
                    autoTestStats.failedUpdates++;
                    log('warning', '⚠️ 位置更新失败，将在下次重试');
                }

                // 更新进度显示
                updateAutoTestProgress();

            } catch (error) {
                log('error', `❌ 自动测试步骤执行失败: ${error.message}`);
                autoTestStats.failedUpdates++;
            }
        }

        // 智能移动到指定站点（模拟真实移动轨迹）
        async function moveToStationIntelligently(stationIndex) {
            const targetStation = stations[stationIndex];
            const currentLat = parseFloat(document.getElementById('latitude').value);
            const currentLng = parseFloat(document.getElementById('longitude').value);

            let newLat, newLng;

            if (targetStation.lat === 0 && targetStation.lng === 0) {
                // 移动到总站（使用固定坐标）
                newLat = 24.580000;
                newLng = 118.100000;
                log('info', `📍 移动到总站: (${newLat}, ${newLng})`);
            } else {
                // 移动到具体站点，添加小幅随机偏移模拟GPS误差
                const offsetRange = 0.0001; // 约10米的GPS误差
                newLat = targetStation.lat + (Math.random() - 0.5) * offsetRange;
                newLng = targetStation.lng + (Math.random() - 0.5) * offsetRange;

                // 计算移动距离
                const distance = calculateDistanceInMeters(currentLat, currentLng, newLat, newLng);
                log('info', `📍 移动到 ${targetStation.name}: (${newLat.toFixed(6)}, ${newLng.toFixed(6)}) 距离: ${distance.toFixed(0)}米`);
            }

            // 更新坐标
            document.getElementById('latitude').value = newLat.toFixed(6);
            document.getElementById('longitude').value = newLng.toFixed(6);
        }

        // 计算两点间距离（米）
        function calculateDistanceInMeters(lat1, lng1, lat2, lng2) {
            const R = 6371000; // 地球半径（米）
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLng = (lng2 - lng1) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                      Math.sin(dLng/2) * Math.sin(dLng/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            return R * c;
        }

        // 带统计的位置更新
        async function sendLocationUpdateWithStats() {
            try {
                await sendLocationUpdate();
                return true;
            } catch (error) {
                log('error', `❌ 位置更新失败: ${error.message}`);
                return false;
            }
        }

        // 重置自动测试统计
        function resetAutoTestStats() {
            autoTestStats = {
                totalUpdates: 0,
                successfulUpdates: 0,
                failedUpdates: 0,
                stationsVisited: 0,
                cyclesCompleted: 0
            };
            autoTestCycleCount = 0;
        }

        // 更新自动测试进度显示
        function updateAutoTestProgress() {
            if (!autoTestRunning) {
                document.getElementById('autoTestProgress').textContent = '未启动';
                document.getElementById('testStats').textContent = '-';
                return;
            }

            const currentStation = stations[currentAutoStationIndex];
            const progressText = `第${autoTestCycleCount + 1}轮 - ${currentStation ? currentStation.name : '未知'}`;
            document.getElementById('autoTestProgress').textContent = progressText;

            const successRate = autoTestStats.totalUpdates > 0 ?
                Math.round((autoTestStats.successfulUpdates / autoTestStats.totalUpdates) * 100) : 0;
            const statsText = `成功率${successRate}% (${autoTestStats.successfulUpdates}/${autoTestStats.totalUpdates})`;
            document.getElementById('testStats').textContent = statsText;
        }

        // 清空日志
        function clearLogs() {
            document.getElementById('logs').innerHTML = 
                '<div class="timestamp">[清空]</div> 日志已清空<br>';
        }

        // 添加日志
        function log(type, message) {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `<div class="timestamp">[${timestamp}]</div> <span class="${type}">${message}</span><br>`;
            logs.innerHTML += logEntry;
            logs.scrollTop = logs.scrollHeight;
        }

        // 页面加载完成后初始化
        window.onload = init;

        // 自动发送心跳和状态监控
        setInterval(() => {
            const carId = document.getElementById('carId').value;
            if (autoTestRunning) {
                log('debug', `💓 自动测试心跳: 车辆${carId} 第${autoTestCycleCount + 1}轮测试进行中`);
            } else {
                log('debug', `💓 系统心跳: 车辆${carId} 状态正常`);
            }
        }, 30000);

        // 自动测试监控和异常恢复
        setInterval(() => {
            if (autoTestRunning) {
                // 检查自动测试是否卡住
                const now = Date.now();
                if (autoTestStartTime && (now - autoTestStartTime) > 300000) { // 5分钟
                    log('warning', '⚠️ 自动测试运行超过5分钟，检查是否需要重启');
                }

                // 检查成功率
                if (autoTestStats.totalUpdates > 10) {
                    const successRate = autoTestStats.successfulUpdates / autoTestStats.totalUpdates;
                    if (successRate < 0.8) {
                        log('warning', `⚠️ 自动测试成功率较低: ${Math.round(successRate * 100)}%`);
                    }
                }
            }
        }, 60000); // 每分钟检查一次

        // 添加多用户并发测试功能
        async function testConcurrentUsers() {
            log('info', '🧪 开始多用户并发测试...');

            const promises = [];
            const userCount = 3;

            for (let i = 0; i < userCount; i++) {
                promises.push(simulateUserSession(i + 1));
            }

            try {
                await Promise.all(promises);
                log('success', '✅ 多用户并发测试完成');
            } catch (error) {
                log('error', `❌ 多用户并发测试失败: ${error.message}`);
            }
        }

        // 模拟用户会话
        async function simulateUserSession(userId) {
            log('info', `👤 用户${userId} 开始会话`);

            // 模拟获取车辆位置
            const response = await fetch('/api/vehicle/locations');
            const result = await response.json();

            if (result.success) {
                log('info', `👤 用户${userId} 获取到${result.data.length}个车辆位置`);
            }

            // 模拟长轮询
            const longPollResponse = await fetch('/api/longpolling/poll?timeout=3');
            if (longPollResponse.status === 200 || longPollResponse.status === 204) {
                log('info', `👤 用户${userId} 长轮询正常`);
            }

            log('info', `👤 用户${userId} 会话结束`);
        }

        // 添加快捷键支持
        document.addEventListener('keydown', function(event) {
            if (event.ctrlKey) {
                switch(event.key) {
                    case 's':
                        event.preventDefault();
                        sendLocationUpdate();
                        break;
                    case 't':
                        event.preventDefault();
                        startAutoTest();
                        break;
                    case 'l':
                        event.preventDefault();
                        testLongPolling();
                        break;
                    case 'r':
                        event.preventDefault();
                        testRedisConnection();
                        break;
                }
            }
        });

        // 显示快捷键提示
        log('info', '⌨️ 快捷键: Ctrl+S发送位置, Ctrl+T自动测试, Ctrl+L长轮询, Ctrl+R测试Redis');

        // 生成自动测试报告
        function generateTestReport() {
            if (!autoTestStartTime) {
                log('warning', '⚠️ 尚未进行自动测试，无法生成报告');
                return;
            }

            const duration = Math.round((Date.now() - autoTestStartTime) / 1000);
            const successRate = autoTestStats.totalUpdates > 0 ?
                Math.round((autoTestStats.successfulUpdates / autoTestStats.totalUpdates) * 100) : 0;

            const report = {
                testStartTime: new Date(autoTestStartTime).toLocaleString(),
                testDuration: `${Math.floor(duration / 60)}分${duration % 60}秒`,
                totalUpdates: autoTestStats.totalUpdates,
                successfulUpdates: autoTestStats.successfulUpdates,
                failedUpdates: autoTestStats.failedUpdates,
                successRate: `${successRate}%`,
                stationsVisited: autoTestStats.stationsVisited,
                cyclesCompleted: autoTestStats.cyclesCompleted,
                averageUpdateInterval: autoTestStats.totalUpdates > 0 ?
                    Math.round(duration / autoTestStats.totalUpdates) : 0
            };

            log('info', '📊 === 自动测试报告 ===');
            log('info', `🕐 测试开始时间: ${report.testStartTime}`);
            log('info', `⏱️ 测试持续时间: ${report.testDuration}`);
            log('info', `📈 更新统计: ${report.successfulUpdates}/${report.totalUpdates} (成功率: ${report.successRate})`);
            log('info', `🚏 访问站点: ${report.stationsVisited}个`);
            log('info', `🔄 完成循环: ${report.cyclesCompleted}次`);
            log('info', `⚡ 平均间隔: ${report.averageUpdateInterval}秒/次`);
            log('info', '📊 === 报告结束 ===');

            return report;
        }

        // 导出测试数据
        function exportTestData() {
            const report = generateTestReport();
            if (!report) return;

            const data = {
                report: report,
                logs: document.getElementById('logs').innerText,
                timestamp: new Date().toISOString()
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `bus-test-report-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            log('success', '✅ 测试报告已导出');
        }
    </script>
</body>
</html>
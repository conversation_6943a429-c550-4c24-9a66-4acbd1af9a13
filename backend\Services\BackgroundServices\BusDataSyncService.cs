using Microsoft.Extensions.Options;
using WeChatBus.Configuration;
using WeChatBus.Models;
using WeChatBus.Services;

namespace WeChatBus.Services.BackgroundServices;

/// <summary>
/// 班车数据同步后台服务
/// </summary>
public class BusDataSyncService : BackgroundService
{
    private readonly IGpsApiService _gpsApiService;
    private readonly IRedisService _redisService;
    private readonly DataSyncConfiguration _config;
    private readonly ILogger<BusDataSyncService> _logger;
    private readonly SemaphoreSlim _semaphore;

    public BusDataSyncService(
        IGpsApiService gpsApiService,
        IRedisService redisService,
        IOptions<DataSyncConfiguration> config,
        ILogger<BusDataSyncService> logger)
    {
        _gpsApiService = gpsApiService;
        _redisService = redisService;
        _config = config.Value;
        _logger = logger;
        _semaphore = new SemaphoreSlim(_config.MaxConcurrentRequests, _config.MaxConcurrentRequests);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("班车数据同步服务启动，同步间隔: {Interval}秒", _config.IntervalSeconds);

        // 等待其他服务启动完成
        await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);

        while (!stoppingToken.IsCancellationRequested)
        {
            if (_config.EnableAutoSync)
            {
                await SyncVehicleLocationsAsync(stoppingToken);
            }

            try
            {
                await Task.Delay(TimeSpan.FromSeconds(_config.IntervalSeconds), stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
        }

        _logger.LogInformation("班车数据同步服务已停止");
    }

    /// <summary>
    /// 同步车辆位置信息
    /// </summary>
    private async Task SyncVehicleLocationsAsync(CancellationToken cancellationToken)
    {
        await _semaphore.WaitAsync(cancellationToken);
        
        try
        {
            // 检查Redis连接状态
            if (!await _redisService.IsConnectedAsync())
            {
                _logger.LogWarning("Redis连接异常，跳过本次同步");
                return;
            }

            // 获取最新的车辆位置信息
            var locations = await _gpsApiService.GetAllVehicleLocationsAsync(cancellationToken);
            
            if (!locations.Any())
            {
                _logger.LogWarning("未获取到车辆位置信息");
                return;
            }

            // 应用映射和状态计算，传递Redis服务用于保存到站记录
            foreach (var location in locations)
            {
                location.ApplyMappingAndStatus(_redisService);
            }

            // 保存到Redis
            await _redisService.SaveVehicleLocationsAsync(locations);

            // 发布位置更新事件
            var updateEvent = new LocationUpdateEvent
            {
                Locations = locations,
                Timestamp = DateTime.UtcNow,
                EventId = Guid.NewGuid().ToString()
            };

            await _redisService.PublishLocationUpdateAsync(updateEvent);

            _logger.LogInformation("成功同步 {Count} 个车辆位置信息", locations.Count);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("车辆位置同步被取消");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "同步车辆位置信息时发生异常");
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <summary>
    /// 手动触发同步
    /// </summary>
    public async Task TriggerSyncAsync()
    {
        _logger.LogInformation("手动触发车辆位置同步");
        await SyncVehicleLocationsAsync(CancellationToken.None);
    }

    public override void Dispose()
    {
        _semaphore?.Dispose();
        base.Dispose();
    }
}

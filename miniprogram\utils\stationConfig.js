// 站点配置文件
// 各线路的详细站点信息

// 2号线站点配置（包含总站）
const line2Stations = [
  {
    id: 'line2_station0',
    name: '总站(班车总站)',
    order: 0,
    latitude: null, // 总站使用班车实时坐标，不设固定坐标
    longitude: null,
    isTerminal: true,
    isStarting: true
  },
  {
    id: 'line2_station1', 
    name: '嘉庚体育馆站',
    order: 1,
    latitude: 24.587788,
    longitude: 118.106925,
    isTerminal: false,
    isStarting: false
  },
  {
    id: 'line2_station2',
    name: '集美旧厂区站', 
    order: 2,
    latitude: 24.596244,
    longitude: 118.097278,
    isTerminal: false,
    isStarting: false
  },
  {
    id: 'line2_station3',
    name: '霞梧路口站',
    order: 3,
    latitude: 24.575976,
    longitude: 118.097407,
    isTerminal: false,
    isStarting: false
  },
  {
    id: 'line2_station4',
    name: '叶厝站',
    order: 4,
    latitude: 24.594314,
    longitude: 118.106702,
    isTerminal: false,
    isStarting: false
  },
  {
    id: 'line2_station5',
    name: '禹州大学城站',
    order: 5,
    latitude: 24.621939,
    longitude: 118.127988,
    isTerminal: false,
    isStarting: false
  },
  {
    id: 'line2_station6',
    name: '洪塘头站',
    order: 6,
    latitude: 24.627757,
    longitude: 118.131023,
    isTerminal: false,
    isStarting: false
  },
  {
    id: 'line2_station7',
    name: '酱文化园站',
    order: 7,
    latitude: 24.649589,
    longitude: 118.143611,
    isTerminal: false,
    isStarting: false
  },
  {
    id: 'line2_station8',
    name: '内厝站',
    order: 8,
    latitude: 24.665478,
    longitude: 118.273764,
    isTerminal: false,
    isStarting: false
  },
  {
    id: 'line2_station9',
    name: '厦华科技有限公司',
    order: 9,
    latitude: 24.675507,
    longitude: 118.280386,
    isTerminal: true,
    isStarting: false
  }
]

// 线路配置映射
const stationConfig = {
  // 2号线 (carId: "1162")
  '1162': {
    lineId: '2',
    lineName: '2号线',
    carName: '闽DZ5829',
    stations: line2Stations,
    isDetailEnabled: true // 启用详细功能
  },
  
  // 其他线路暂时只有基本信息
  '1181': {
    lineId: '1', 
    lineName: '1号线',
    carName: '闽DY1576',
    stations: [],
    isDetailEnabled: false
  },
  '1171': {
    lineId: '3',
    lineName: '3号线', 
    carName: '闽DX1686',
    stations: [],
    isDetailEnabled: false
  },
  '5699': {
    lineId: '4',
    lineName: '4号线',
    carName: '闽DX3180', 
    stations: [],
    isDetailEnabled: false
  },
  '1168': {
    lineId: '5',
    lineName: '5号线',
    carName: '闽DZ9581',
    stations: [],
    isDetailEnabled: false
  }
}

/**
 * 根据carId获取线路配置
 * @param {string} carId 车辆ID
 * @returns {object|null} 线路配置对象
 */
function getLineConfig(carId) {
  return stationConfig[carId] || null
}

/**
 * 根据carId获取站点列表
 * @param {string} carId 车辆ID  
 * @returns {array} 站点列表
 */
function getStationsByCarId(carId) {
  const config = getLineConfig(carId)
  return config ? config.stations : []
}

/**
 * 判断线路是否启用详细功能
 * @param {string} carId 车辆ID
 * @returns {boolean} 是否启用详细功能
 */
function isDetailEnabled(carId) {
  const config = getLineConfig(carId)
  return config ? config.isDetailEnabled : false
}

/**
 * 计算两点间距离（米）
 * @param {number} lat1 纬度1
 * @param {number} lng1 经度1  
 * @param {number} lat2 纬度2
 * @param {number} lng2 经度2
 * @returns {number} 距离（米）
 */
function calculateDistance(lat1, lng1, lat2, lng2) {
  const R = 6371000 // 地球半径（米）
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLng = (lng2 - lng1) * Math.PI / 180
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
            Math.sin(dLng/2) * Math.sin(dLng/2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
  return R * c
}

/**
 * 根据站点索引获取站点坐标（纯查询功能）
 * @param {string} carId 车辆ID
 * @param {number} stationIndex 站点索引
 * @returns {object|null} 站点坐标信息
 */
function getStationCoordinates(carId, stationIndex) {
  const stations = getStationsByCarId(carId)
  if (stations.length === 0 || stationIndex < 0 || stationIndex >= stations.length) {
    return null
  }

  const station = stations[stationIndex]
  return {
    id: station.id,
    name: station.name,
    latitude: station.latitude,
    longitude: station.longitude,
    order: station.order,
    isTerminal: station.isTerminal
  }
}
import md5 from '../utils/md5.min';

/**
 * 计算到目标站点的信息（使用腾讯地图接口 - 准确版本）
 * @param {string} carId 车辆ID
 * @param {number} currentLat 当前纬度
 * @param {number} currentLng 当前经度
 * @param {number} targetStationIndex 目标站点索引
 * @param {number} currentSpeed 当前车辆速度（km/h）
 * @param {boolean} hasServerSpeed 是否从服务器获取到车速数据
 * @returns {Promise<object>} 包含站数、距离、预估时间的对象
 */
/**
 * 使用后端API计算到目标站点的信息（替代腾讯地图直接调用）
 * @param {string} carId 车辆ID
 * @param {number} currentLat 当前纬度
 * @param {number} currentLng 当前经度
 * @param {number} targetStationIndex 目标站点索引
 * @param {number} currentSpeed 当前车辆速度（km/h）
 * @param {boolean} hasServerSpeed 是否从服务器获取到车速数据
 * @returns {Promise<object>} 包含站数、距离、预估时间的对象
 */
async function calculateStationInfoWithBackendAPI(carId, currentLat, currentLng, targetStationIndex, currentSpeed = 25, hasServerSpeed = false) {
  const request = require('./request')
  
  try {
    console.log(`使用后端API计算路径规划`)
    console.log(`起点: (${currentLat}, ${currentLng})`)
    console.log(`目标站点索引: ${targetStationIndex}`)
    console.log(`车速信息: ${currentSpeed}km/h (来自服务器: ${hasServerSpeed})`)

    // 获取目标站点坐标
    const targetCoords = getStationCoordinates(carId, targetStationIndex)
    if (!targetCoords) {
      return { stationCount: 0, distance: 0, estimatedTime: 0 }
    }

    // 调用后端API计算距离和预估时间
    const response = await request.post('/vehicle/calculate-route', {
      fromLat: currentLat,
      fromLng: currentLng,
      toLat: targetCoords.latitude,
      toLng: targetCoords.longitude,
      carId: carId,
      targetStationIndex: targetStationIndex,
      currentSpeed: currentSpeed,
      hasServerSpeed: hasServerSpeed
    })

    if (response.success && response.data) {
      console.log('后端API计算结果:', response.data)
      return {
        stationCount: response.data.stationCount || Math.max(1, targetStationIndex),
        distance: response.data.distance || 0,
        estimatedTime: response.data.estimatedTime || 0,
        routeInfo: {
          hasTraffic: response.data.hasTraffic || false,
          isBackup: response.data.isBackup || false
        }
      }
    } else {
      throw new Error(response.message || '后端API调用失败')
    }
  } catch (error) {
    console.error('后端API路线计算失败:', error)
    
    // 降级到简单计算方式
    const directDistance = calculateDistance(currentLat, currentLng, 
      getStationCoordinates(carId, targetStationIndex)?.latitude || 0,
      getStationCoordinates(carId, targetStationIndex)?.longitude || 0)
    const roadFactor = 1.5
    const actualDistance = directDistance * roadFactor
    const avgSpeed = 18 // km/h
    const estimatedTime = Math.max(2, Math.round((actualDistance / 1000) / avgSpeed * 60))
    
    return {
      stationCount: Math.max(1, targetStationIndex),
      distance: Math.round(actualDistance),
      estimatedTime,
      routeInfo: {
        hasTraffic: false,
        isBackup: true
      }
    }
  }
}

/**
 * 格式化距离显示
 * @param {number} distance 距离（米）
 * @returns {string} 格式化后的距离字符串
 */
function formatDistance(distance) {
  if (distance >= 1000) {
    return `${(distance / 1000).toFixed(1)}公里`
  } else {
    return `${distance}米`
  }
}

/**
 * 使用改进的路径规划计算路线距离和时间（兜底方案）
 * @param {number} fromLat 起点纬度
 * @param {number} fromLng 起点经度  
 * @param {number} toLat 终点纬度
 * @param {number} toLng 终点经度
 * @returns {Promise<object>} 包含距离和时间的对象
 */
function calculateRouteByWXRoute(fromLat, fromLng, toLat, toLng) {
  return new Promise((resolve, reject) => {
    console.log('使用改进的路径规划计算')
    
    // 计算直线距离并应用合理的道路系数
    const directDistance = calculateDistance(fromLat, fromLng, toLat, toLng)
    const roadDistance = directDistance * 1.4 // 道路弯曲系数
    
    // 使用合理的城市行驶速度计算时间
    const avgSpeed = 18 // km/h
    const travelTime = (roadDistance / 1000) / avgSpeed * 3600 // 秒
    
    // 加入适度的城市延误
    const cityDelay = Math.max(120, roadDistance / 1000 * 45) // 每公里45秒延误
    const totalTime = travelTime + cityDelay
    
    console.log('改进路径规划计算结果:', {
      directDistance: directDistance.toFixed(0) + '米',
      roadDistance: roadDistance.toFixed(0) + '米', 
      travelTime: (travelTime / 60).toFixed(1) + '分钟',
      cityDelay: (cityDelay / 60).toFixed(1) + '分钟',
      totalTime: (totalTime / 60).toFixed(1) + '分钟'
    })
    
    resolve({
      distance: Math.round(roadDistance),
      duration: Math.round(totalTime / 60), // 转换为分钟
      hasTraffic: false,
      isWXRoute: true
    })
  })
}

module.exports = {
  getLineConfig,
  getStationsByCarId,
  isDetailEnabled,
  calculateDistance,
  getStationCoordinates,
  calculateStationInfoWithBackendAPI,
  calculateRouteByWXRoute,
  formatDistance,
  stationConfig
}
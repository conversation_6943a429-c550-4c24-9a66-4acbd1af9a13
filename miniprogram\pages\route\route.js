// pages/route/route.js
const app = getApp()
const { longPollingService } = require('../../utils/longPolling')
const request = require('../../utils/request')
const util = require('../../utils/util')

// 延迟加载 stationConfig 以避免循环依赖
let stationConfig = null
function getStationConfig() {
  if (!stationConfig) {
    stationConfig = require('../../utils/stationConfig')
  }
  return stationConfig
}

Page({
  data: {
    routeData: null,
    routeInfo: null,
    stations: [],
    passedStationIds: [],
    busLocation: null,
    isLoading: false,
    lastUpdateTime: '',
    autoRefreshEnabled: false,
    selectedStation: null, // 选中的站点信息
    targetStationIndex: -1, // 目标站点索引
    busLocationText: '', // 班车当前位置描述文本
    nextBusStationText:'', //班车下一站位置描述文本
    currentStationIndex: -1, // 当前站点索引
    nextStationIndex: -1, // 下一站点索引
    hasError: false,
    errorMessage: '',
    errorTitle: '',
    showRetry: false,
    // API超时设置
    apiTimeout: 15000, // 15秒超时
    // 添加站点计算触发相关状态
    lastKnownStationIndex: -1, // 上次已知的站点索引
    stationCalculationEnabled: true, // 是否启用站点计算
    lastCalculationTrigger: '', // 上次计算触发原因
    calculationCount: 0, // 计算次数统计
    // 防重复调用
    isCalculating: false, // 是否正在计算中
    lastCalculationTime: 0, // 上次计算时间戳
    calculationCooldown: 3000, // 计算冷却时间（毫秒）
    // 页面级防重复机制
    isPageProcessing: false, // 页面是否正在处理数据
    lastPageProcessTime: 0, // 上次页面处理时间
    pageProcessCooldown: 2000 // 页面处理冷却时间缩短到2秒
  },

  onLoad(options) {
    try {
      // 从URL参数中解析完整的线路数据
      const routeData = JSON.parse(decodeURIComponent(options.routeData))

      console.log('详情页面接收到的路线数据:', routeData)

      this.setData({
        routeData: routeData,
        routeInfo: {
          id: routeData.id,
          name: routeData.name,
          carNumber: routeData.carNumber,
          color: routeData.color
        },
        stations: routeData.stations.map(station => ({
          ...station,
          status: station.isPassed ? 'passed' : 'waiting'
        })),
        passedStationIds: routeData.passedStationIds || [],
        // 设置默认值避免跳闪
        busLocationText: '正在获取位置...',
        nextBusStationText: '正在获取下一站...',
        selectedStation: null // 改为null，等待API获取真实数据
      })

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: routeData.name
      })

      // 模拟站点状态（用于演示动画效果）
      this.simulateStationStatus()

      // 添加动画调试
      this.debugAnimationStatus()

      // 尝试从全局服务获取车辆位置数据
      const globalLocation = app.getVehicleLocation(routeData.id)
      if (globalLocation) {
        console.log('使用全局缓存的车辆位置数据')
        this.processLocationData(globalLocation)
      } else {
        // 如果没有全局数据，则主动获取（使用超时控制）
        this.getBusLocationWithTimeout()
      }

      // 注意：不再启动独立的长轮询，使用全局服务

      // 默认选中终点站
      this.setDefaultTargetStation()

      // 延迟执行自动滚动，确保页面渲染完成和数据加载完成
      setTimeout(() => {
        this.scrollToCurrentPosition()
      }, 1000)

      // 页面打开时触发一次预估计算（规则2）
      setTimeout(() => {
        if (this.data.targetStationIndex !== -1 && this.data.busLocation) {
          this.triggerStationCalculation('页面打开', this.data.targetStationIndex)
        }
      }, 1500)

    } catch (error) {
      console.error('解析线路数据失败:', error)
      this.handleLoadError('线路数据解析失败', error)
    }
  },

  // 带超时的获取班车位置
  async getBusLocationWithTimeout() {
    try {
      this.setData({ isLoading: true })

      // 创建超时Promise
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('请求超时')), this.data.apiTimeout)
      })

      // 使用Promise.race实现超时控制
      await Promise.race([
        this.getBusLocation(),
        timeoutPromise
      ])
      
    } catch (error) {
      console.error('获取班车位置超时或失败:', error)
      if (error.message === '请求超时') {
        this.handleLoadError('请求超时', '获取班车位置信息超时，请检查网络连接')
      } else {
        this.handleLoadError('获取失败', error.message || '获取班车位置信息失败')
      }
    } finally {
      this.setData({ isLoading: false })
    }
  },

  // 简化的错误处理
  handleLoadError(title, error) {
    console.error('加载错误:', title, error)
    
    let errorMessage = '请检查网络连接后重试'
    if (typeof error === 'string') {
      errorMessage = error
    } else if (error && error.message) {
      errorMessage = error.message
    }
    
    this.setData({
      hasError: true,
      errorTitle: title,
      errorMessage: errorMessage,
      isLoading: false
    })
  },

  // 重新加载页面数据
  retryLoad() {
    console.log('用户点击重新加载')
    this.setData({
      hasError: false,
      errorMessage: '',
      errorTitle: ''
    })
    
    // 重新获取位置信息
    this.getBusLocationWithTimeout()
  },

  // 调试动画状态
  debugAnimationStatus() {
    console.log('=== 动画调试开始 ===')
    setTimeout(() => {
      const stations = this.data.stations
      console.log('当前所有站点状态:')
      stations.forEach((station, index) => {
        console.log(`站点 ${index + 1}: ${station.name} - 状态: ${station.status}`)
        if (station.status === 'current') {
          console.log('🚌 动画应该在这个站点显示!')
        }
      })
      
      const currentStations = stations.filter(s => s.status === 'current')
      if (currentStations.length === 0) {
        console.error('⚠️ 没有找到current状态的站点，动画不会显示!')
      } else if (currentStations.length > 1) {
        console.warn('⚠️ 找到多个current状态的站点，可能有问题')
      } else {
        console.log('✅ 找到1个current状态的站点，动画应该正常显示')
      }
      console.log('=== 动画调试结束 ===')
    }, 200)
  },

  // 简化初始状态：等待后端数据
  simulateStationStatus() {
    // 设置最简单的初始状态，等待后端数据
    const stations = this.data.stations.map((station, index) => ({
      ...station,
      status: index === 0 ? 'current' : 'waiting' // 默认总站为当前站点
    }))
    
    this.setData({ 
      stations: stations,
      busLocationText: '等待位置数据...',
      nextBusStationText: '等待下一站信息...'
    })
    
    console.log('初始状态设置完成，等待后端数据')
  },

  // 设置默认目标站点（下一站）
  setDefaultTargetStation() {
    const stations = this.data.stations
    const currentIndex = this.data.currentStationIndex

    console.log('设置默认目标站点，当前站点索引:', currentIndex, '总站点数:', stations.length)

    if (stations.length > 0) {
      let defaultTargetIndex = -1
      
      // 检查当前站点状态
      if (currentIndex === 0) {
        // 在总站（索引0），下一站应该是嘉庚体育馆站（索引1）
        console.log('当前在总站，下一站选择嘉庚体育馆站')
        defaultTargetIndex = 1
      } else if (currentIndex !== -1) {
        // 在其他站点，选择下一站
        defaultTargetIndex = currentIndex + 1
        
        // 如果下一站不存在，则选择第一个未到达的站点
        if (defaultTargetIndex >= stations.length) {
          for (let i = currentIndex + 1; i < stations.length; i++) {
            if (stations[i].status === 'waiting') {
              defaultTargetIndex = i
              break
            }
          }
        }
      } else {
        // 无法确定当前站点，默认选择嘉庚体育馆站
        console.log('无法确定当前站点，默认选择嘉庚体育馆站')
        defaultTargetIndex = 1
      }

      console.log('选择的默认目标站点索引:', defaultTargetIndex)

      // 确保目标站点有效
      if (defaultTargetIndex >= 0 && defaultTargetIndex < stations.length) {
        const targetStation = stations[defaultTargetIndex]
        if (targetStation && targetStation.status === 'waiting') {
          this.setTargetStation(defaultTargetIndex)
        } else {
          console.log('目标站点状态不是waiting，等待API获取真实数据')
          // 标记目标站点，但不设置写死的预估信息
          this.setData({
            targetStationIndex: defaultTargetIndex
          })
          // 等待API获取位置信息后再计算预估信息
        }
      } else {
        // 兜底：选择嘉庚体育馆站，等待API获取真实数据
        console.log('兜底选择嘉庚体育馆站，等待API获取真实数据')
        this.setData({
          targetStationIndex: 1
        })
        // 等待API获取位置信息后再计算预估信息
      }
    }
  },

  // 自动滚动到当前班车位置区间
  scrollToCurrentPosition() {
    const currentIndex = this.data.currentStationIndex
    if (currentIndex === -1) return

    // 计算滚动位置：滚动到当前站点，考虑站点宽度和间距
    const stationWidth = 100 // 站点最小宽度
    const stationMargin = 70 // 站点右边距
    const scrollLeft = Math.max(0, (currentIndex - 1) * (stationWidth + stationMargin))

    console.log(`自动滚动到位置: currentIndex=${currentIndex}, scrollLeft=${scrollLeft}`)

    // 设置水平滚动位置
    this.setData({
      scrollLeft: scrollLeft
    })
  },

  // ===== 站点计算触发规则实现 =====

  /**
   * 触发站点计算（优化的防重复调用机制）
   * @param {string} trigger 触发原因
   * @param {number} targetIndex 目标站点索引
   */
  async triggerStationCalculation(trigger, targetIndex = -1) {
    if (!this.data.stationCalculationEnabled) {
      console.log('站点计算已禁用，跳过')
      return
    }

    // 优化的防重复调用检查 - 缩短冷却时间，允许用户交互触发
    const now = Date.now()

    // 如果是用户点击触发，优先处理，缩短冷却时间
    const isUserTrigger = trigger === '用户点击站点'
    const cooldownTime = isUserTrigger ? 500 : this.data.calculationCooldown // 用户点击只需0.5秒冷却

    if (this.data.isCalculating && !isUserTrigger) {
      console.log(`站点计算进行中，跳过重复调用: ${trigger}`)
      return
    }

    // 用户点击不同站点时允许立即计算
    if (!isUserTrigger && now - this.data.lastCalculationTime < cooldownTime) {
      console.log(`站点计算冷却中，跳过调用: ${trigger} (距离上次${now - this.data.lastCalculationTime}ms)`)
      return
    }

    // 用户点击相同站点时进行冷却检查
    if (isUserTrigger && targetIndex === this.data.targetStationIndex &&
        now - this.data.lastCalculationTime < cooldownTime) {
      console.log(`用户重复点击相同站点，跳过计算`)
      return
    }

    const busLocation = this.data.busLocation
    const routeData = this.data.routeData

    if (!busLocation || !routeData || !routeData.isDetailEnabled) {
      console.log('缺少必要数据或未启用详细功能，跳过站点计算')
      return
    }

    // 对于用户点击，如果正在计算中，先停止当前计算
    if (isUserTrigger && this.data.isCalculating) {
      console.log('用户点击触发，停止当前计算')
      this.setData({ isCalculating: false })
    }

    // 设置计算状态
    this.setData({ 
      isCalculating: true,
      lastCalculationTime: now
    })

    const calculationCount = this.data.calculationCount + 1
    this.setData({ 
      calculationCount,
      lastCalculationTrigger: trigger 
    })

    console.log(`=== 站点计算触发 (第${calculationCount}次) ===`)
    console.log(`触发原因: ${trigger}`)
    console.log(`目标站点索引: ${targetIndex}`)
    console.log(`是否用户触发: ${isUserTrigger}`)

    try {
      // 确定要计算的目标站点
      let finalTargetIndex = targetIndex
      if (finalTargetIndex === -1) {
        finalTargetIndex = this.data.targetStationIndex
      }
      if (finalTargetIndex === -1) {
        console.log('没有有效的目标站点，跳过计算')
        return
      }

      // 调用计算方法
      await this.calculateAndShowEstimatedInfo(finalTargetIndex)
      
      console.log(`站点计算完成: ${trigger}`)
    } catch (error) {
      console.error('站点计算失败:', error)
      
      // 对于用户点击失败，显示友好提示
      if (isUserTrigger) {
        wx.showToast({
          title: '计算失败，请稍后重试',
          icon: 'none',
          duration: 1500
        })
      }
    } finally {
      // 重置计算状态，缩短重置时间
      setTimeout(() => {
        this.setData({ isCalculating: false })
      }, isUserTrigger ? 500 : 1000) // 用户触发更快重置
    }
  },

  /**
   * 检测站点变化并触发计算（规则1：经过一站往下一站）
   */
  checkStationChangeAndTrigger() {
    const currentStationIndex = this.data.currentStationIndex
    const lastKnownIndex = this.data.lastKnownStationIndex

    console.log(`检测站点变化: 当前=${currentStationIndex}, 上次=${lastKnownIndex}`)

    // 如果站点发生变化（往前进）
    if (currentStationIndex !== lastKnownIndex && currentStationIndex > lastKnownIndex) {
      console.log(`检测到站点前进: ${lastKnownIndex} -> ${currentStationIndex}`)
      
      // 更新记录的站点索引
      this.setData({ lastKnownStationIndex: currentStationIndex })
      // 触发规则1：经过了一站往下一站，触发一次计算
      this.triggerStationCalculation('站点前进', this.data.targetStationIndex)
    } else if (lastKnownIndex === -1 && currentStationIndex !== -1) {
      // 首次确定站点位置
      this.setData({ lastKnownStationIndex: currentStationIndex })
      console.log(`首次确定站点位置: ${currentStationIndex}`)
    }
  },

  // 设置目标站点（优化用户体验）
  setTargetStation(stationIndex) {
    const stations = this.data.stations
    const station = stations[stationIndex]

    console.log('设置目标站点:', stationIndex, station)

    if (!station || station.status !== 'waiting') {
      console.log('目标站点无效或不是等待状态')
      return
    }

    // 立即更新目标站点，提供即时反馈
    this.setData({
      targetStationIndex: stationIndex
    })

    // 显示计算中的状态，让用户知道正在处理
    this.setData({
      selectedStation: {
        stationCount: 0,
        estimatedTime: 0,
        distance: 0,
        stationName: station.name,
        targetIndex: stationIndex,
        formattedDistance: '计算中...',
        isCalculating: true
      }
    })
    // 触发规则2：自由点击到其他未经过或未到达的站点，触发一次计算
    this.triggerStationCalculation('用户点击站点', stationIndex)
  },

  /**
   * 计算并显示预估信息（纯预估功能，不做状态判定）
   * @param {*} targetStationIndex 
   */
  async calculateAndShowEstimatedInfo(targetStationIndex) {
    const routeData = this.data.routeData
    const busLocation = this.data.busLocation
    if (!routeData || !busLocation || !routeData.isDetailEnabled) {
      // 对于非详细功能线路，使用简单预估
      const targetStation = this.data.stations[targetStationIndex]
      if (!targetStation) {
        console.log('目标站点不存在')
        return
      }
      // 简单预估：基于站点索引
      const stationCount = Math.max(1, targetStationIndex)
      const estimatedTime = Math.max(1, stationCount * 2 + Math.floor(Math.random() * stationCount))
      const distance = stationCount * 530 + Math.floor(Math.random() * 270)

      this.setData({
        selectedStation: {
          stationCount: stationCount,
          estimatedTime: estimatedTime,
          distance: distance,
          stationName: targetStation.name,
          targetIndex: targetStationIndex,
          formattedDistance: distance >= 1000 ? (distance / 1000).toFixed(1) + '公里' : distance + '米'
        }
      })
      return
    }

    // 对于启用详细功能的线路，使用地图接口精确预估
    const carId = routeData.originalData?.carId || routeData.id
    const currentSpeed = busLocation.speed || 25

    try {
      console.log('使用地图接口进行预估计算，车辆速度:', currentSpeed)
      const stationInfo = await getStationConfig().calculateStationInfoWithBackendAPI(
        carId,
        busLocation.lat || busLocation.latitude,
        busLocation.lng || busLocation.longitude,
        targetStationIndex,
        currentSpeed,
        !!(busLocation.speed && busLocation.speed > 0)
      )

      const targetStation = this.data.stations[targetStationIndex]
      if (!targetStation) {
        console.log('目标站点不存在')
        return
      }

      console.log('预估计算结果:', stationInfo)

      // 设置预估结果
      this.setData({
        selectedStation: {
          stationCount: stationInfo.stationCount,
          estimatedTime: stationInfo.estimatedTime,
          distance: stationInfo.distance,
          stationName: targetStation.name,
          targetIndex: targetStationIndex,
          formattedDistance: getStationConfig().formatDistance(stationInfo.distance)
        }
      })

      console.log('预估信息设置完成:', this.data.selectedStation)
    } catch (error) {
      console.error('预估计算失败:', error)

      // 显示计算失败状态，提供降级信息
      const targetStation = this.data.stations[targetStationIndex]
      if (targetStation) {
        // 提供简单的降级预估
        const fallbackStationCount = Math.max(1, targetStationIndex)
        const fallbackTime = fallbackStationCount * 3 + 2 // 简单预估：每站3分钟+2分钟余量
        const fallbackDistance = fallbackStationCount * 800 // 简单预估：每站800米

        this.setData({
          selectedStation: {
            stationCount: fallbackStationCount,
            estimatedTime: fallbackTime,
            distance: fallbackDistance,
            stationName: targetStation.name,
            targetIndex: targetStationIndex,
            formattedDistance: fallbackDistance >= 1000 ?
              (fallbackDistance / 1000).toFixed(1) + '公里' : fallbackDistance + '米',
            isCalculating: false,
            isFallback: true // 标记为降级数据
          }
        })

        // 显示友好的错误提示
        wx.showToast({
          title: '使用预估数据',
          icon: 'none',
          duration: 1000
        })
      }
    }
  },

  onShow() {
    console.log('详情页面显示')
    
    // 页面显示时检查全局数据
    const routeCarId = this.data.routeData?.originalData?.carId || this.data.routeData?.id
    const globalLocation = app.getVehicleLocation(routeCarId)
    if (globalLocation) {
      console.log('页面显示时使用全局数据')
      this.processLocationData(globalLocation)
    } else if (!this.data.busLocation && !this.data.isLoading && !this.data.hasError) {
      // 如果没有位置数据且不在加载中，主动获取
      console.log('页面显示时重新获取位置数据')
      this.getBusLocationWithTimeout()
    }
  },

  // 全局位置更新回调（由app.js调用）
  onGlobalLocationUpdate(locations) {
    const routeCarId = this.data.routeData?.originalData?.carId || this.data.routeData?.id
    const vehicleLocation = locations.find(loc =>
      loc.carId === routeCarId || loc.carId === String(routeCarId)
    )

    if (vehicleLocation) {
      console.log('详情页收到全局位置更新:', vehicleLocation)
      this.processLocationData(vehicleLocation)
    }
  },

  // 处理位置数据的统一方法（纯"看图说话"版本）
  processLocationData(vehicleLocation) {
    console.log('收到后端位置数据，开始纯渲染模式:', vehicleLocation)

    // 1. 直接使用后端数据更新位置信息（零状态判定）
    const busLocation = {
      latitude: vehicleLocation.lat,
      longitude: vehicleLocation.lng,
      lat: vehicleLocation.lat,
      lng: vehicleLocation.lng,
      address: vehicleLocation.addr || '位置获取中',
      speed: vehicleLocation.speed || 0,
      updateTime: this.formatTime(this.parseLocationTime(vehicleLocation.time)),
      isOnline: vehicleLocation.isOnline,
      state: vehicleLocation.state
    }

    // 2. 直接渲染后端提供的站点描述（不做任何判定）
    const busLocationText = vehicleLocation.currentStation ? `【${vehicleLocation.currentStation}】` : '位置获取中...'
    const nextBusStationText = vehicleLocation.nextStation && vehicleLocation.nextStation !== '前端计算中' 
      ? (vehicleLocation.nextStation === "已到站" ? '已到站' : `【${vehicleLocation.nextStation}】`) 
      : '计算中...'

    // 3. 直接使用后端的已过站点数据渲染站点状态（纯渲染）
    let updatedStations = [...this.data.stations]
    let passedStationIds = []
    
    if (vehicleLocation.passedStationIds) {
      try {
        passedStationIds = JSON.parse(vehicleLocation.passedStationIds)
        // 纯渲染：根据后端数据设置站点状态
        updatedStations = this.data.stations.map((station, index) => {
          if (passedStationIds.includes(station.id)) {
            return { ...station, status: 'passed' }
          } else if (vehicleLocation.currentStationIndex === index) {
            return { ...station, status: 'current' }
          } else {
            return { ...station, status: 'waiting' }
          }
        })
      } catch (error) {
        console.error('解析站点数据失败，保持当前状态:', error)
      }
    }

    // 4. 一次性更新所有显示数据
    const oldCurrentStationIndex = this.data.currentStationIndex
    
    this.setData({
      busLocation: busLocation,
      busLocationText: busLocationText,
      nextBusStationText: nextBusStationText,
      stations: updatedStations,
      passedStationIds: passedStationIds,
      currentStationIndex: vehicleLocation.currentStationIndex || -1,
      lastUpdateTime: this.formatTime(new Date()),
      isLoading: false
    })

    // 5. 更新进度线（纯渲染）
    this.updateProgressLine(passedStationIds)
    
    // 6. 检测站点变化并触发计算（规则3：后端接口切换下一站时触发）
    const newCurrentStationIndex = vehicleLocation.currentStationIndex || -1
    if (oldCurrentStationIndex !== -1 && newCurrentStationIndex !== -1 && 
        oldCurrentStationIndex !== newCurrentStationIndex && 
        newCurrentStationIndex > oldCurrentStationIndex) {
      console.log(`检测到后端站点切换: ${oldCurrentStationIndex} -> ${newCurrentStationIndex}`)
      // 更新记录的站点索引
      this.setData({ lastKnownStationIndex: newCurrentStationIndex })
      // 触发一次计算
      if (this.data.targetStationIndex !== -1) {
        this.triggerStationCalculation('后端站点切换', this.data.targetStationIndex)
      }
    }

    console.log('纯渲染模式完成')
  },

  // 简化的长轮询数据处理
  onLongPollingData(data, error) {
    if (error) {
      console.error('长轮询错误:', error)
      return
    }

    console.log('收到长轮询数据:', data)

    // 检查数据格式并提取车辆位置
    let locations = []
    if (data && data.locations && Array.isArray(data.locations)) {
      locations = data.locations
    } else if (data && Array.isArray(data)) {
      locations = data
    } else if (data && data.data && Array.isArray(data.data)) {
      locations = data.data
    }

    if (locations.length > 0) {
      this.processLocationUpdate(locations)
    }
  },

  // 处理位置更新数据（简化版）
  processLocationUpdate(locations) {
    const routeCarId = this.data.routeData?.originalData?.carId || this.data.routeData?.id
    const vehicleLocation = locations.find(loc =>
      loc.carId === routeCarId || loc.carId === String(routeCarId)
    )

    if (vehicleLocation) {
      console.log('找到当前车辆位置数据，直接渲染:', vehicleLocation)
      this.processLocationData(vehicleLocation)
    } else {
      console.log('未找到当前车辆的位置数据，carId:', routeCarId)
    }
  },

  // 更新进度线（纯渲染）
  updateProgressLine(passedStationIds) {
    const totalStations = this.data.stations.length
    const passedCount = passedStationIds.length
    const progressPercentage = totalStations > 0 ? (passedCount / totalStations) * 100 : 0

    const progressStyle = `--passed-percentage: ${progressPercentage}%`
    this.setData({ progressStyle: progressStyle })

    console.log(`进度更新: ${passedCount}/${totalStations} = ${progressPercentage}%`)
  },

  // 解析位置时间（处理后端返回的字符串时间格式）
  parseLocationTime(timeStr) {
    if (!timeStr) {
      return new Date()
    }
    
    // 如果已经是Date对象，直接返回
    if (timeStr instanceof Date) {
      return timeStr
    }
    
    // 处理后端返回的时间字符串格式: "2025-01-15 14:30:25"
    if (typeof timeStr === 'string') {
      // 替换空格为T，添加时区信息以确保正确解析
      const isoString = timeStr.replace(' ', 'T') + (timeStr.includes('+') ? '' : '+08:00')
      const parsedDate = new Date(isoString)
      
      // 如果解析失败，尝试直接new Date
      if (isNaN(parsedDate.getTime())) {
        return new Date(timeStr) || new Date()
      }
      
      return parsedDate
    }
    
    return new Date()
  },

  // 格式化时间
  formatTime(date) {
    if (!date || isNaN(date.getTime())) {
      date = new Date()
    }
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const seconds = date.getSeconds().toString().padStart(2, '0')
    return `${hours}:${minutes}:${seconds}`
  },

  // 获取站点状态文本
  getStationStatusText(status) {
    const statusMap = {
      'waiting': '未到达',
      'current': '当前位置',
      'passed': '已过'
    }
    return statusMap[status] || '未知'
  },

  // 错误处理函数
  handleError(message, error) {
    console.error(message, error)

    // 设置错误状态
    this.setData({
      errorMessage: message || '页面加载失败',
      isLoading: false
    })

    // 显示错误提示
    wx.showToast({
      title: message || '加载失败',
      icon: 'none',
      duration: 2000
    })
  },

  // 位置信息错误处理（不影响页面基本显示）
  handleLocationError(message, error) {
    console.error(message, error)

    // 只显示提示，不影响页面显示
    if (this.data.isLoading) {
      wx.showToast({
        title: message || '位置信息获取失败',
        icon: 'none',
        duration: 1500
      })
    }
  },

  // 重试加载
  retryLoad() {
    console.log('用户点击重试')
    this.setData({
      hasError: false,
      errorMessage: '',
      showRetry: false,
      isLoading: true
    })

    // 重新获取位置信息
    this.getBusLocation()
  },

  // 站点点击事件处理
  onStationTap(e) {
    const stationIndex = parseInt(e.currentTarget.dataset.index)
    const station = this.data.stations[stationIndex]

    console.log('用户点击站点:', stationIndex, station)

    // 只允许点击等待状态的站点
    if (!station || station.status !== 'waiting') {
      wx.showToast({
        title: '只能选择未到达的站点',
        icon: 'none',
        duration: 1500
      })
      return
    }

    // 调用现有的设置目标站点方法
    this.setTargetStation(stationIndex)
  }
})

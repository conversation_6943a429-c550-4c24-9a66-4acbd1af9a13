<!--pages/route/route.wxml-->
<view class="container">
  <!-- 超时错误提示 -->
  <view class="error-container" wx:if="{{hasError}}">
    <view class="error-content">
      <text class="error-icon">⚠️</text>
      <text class="error-title">{{errorTitle || '加载失败'}}</text>
      <text class="error-message">{{errorMessage}}</text>
      <view class="error-actions">
        <button class="retry-btn" bindtap="retryLoad">重新加载</button>
        <button class="refresh-btn-alt" bindtap="onRefresh">手动刷新</button>
      </view>
    </view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content" wx:if="{{!hasError}}">
    <!-- 班车信息卡片 -->
    <view class="bus-info-card">
      <view class="bus-header">
        <view class="bus-icon" style="background-color: {{routeInfo.color}}">
          <text class="icon-text">🚌</text>
        </view>
        <view class="bus-details">
          <text class="bus-name">{{routeInfo.name}}</text>
          <text class="bus-number">{{routeInfo.carNumber}}</text>
        </view>
        <view class="header-actions">
          <view class="refresh-btn" bindtap="onRefresh">
            <text class="refresh-icon">🔄</text>
          </view>
        </view>
      </view>

      <view class="bus-status" wx:if="{{busLocation}}">
        <view class="status-item">
          <text class="status-label">班车位置：</text>
          <text class="status-value-highlight">{{busLocationText}}</text>
          <!-- 到站标记提示 -->
          <text class="arrived-badge" wx:if="{{busLocation.isAtStation}}">✅已到站</text>
        </view>
        <view class="status-item">
          <text class="status-label">正在前往</text>
          <text class="status-value-highlight">{{nextBusStationText}}</text>
        </view>
        <!-- 详细班车位置信息 -->
        <view class="status-item detail-location">
          <text class="detail-label">详细位置：</text>
          <text class="detail-value-full">{{busLocation.address || '位置获取中...'}}</text>
        </view>
      </view>

      <view class="loading" wx:if="{{isLoading}}">
        <text class="loading-text">正在获取班车位置...</text>
      </view>
    </view>

    <!-- 预估时间显示卡片 -->
    <view class="estimated-card" wx:if="{{selectedStation}}">
      <view class="estimated-content">
        <text class="estimated-title">预估到站</text>
        <text class="estimated-detail" wx:if="{{!selectedStation.isCalculating}}">{{selectedStation.stationCount}}站 / {{selectedStation.estimatedTime}}分钟 / {{selectedStation.formattedDistance}}</text>
        <text class="estimated-detail calculating" wx:if="{{selectedStation.isCalculating}}">正在计算预估信息...</text>
      </view>
    </view>

    <!-- 线路站点 -->
    <view class="route-section">
      <view class="section-header">
        <text class="section-title">线路站点</text>
        <text class="section-desc">左右滑动查看更多站点，点击未到站点查看预估时间</text>
      </view>

      <scroll-view
        class="stations-container"
        scroll-x="true"
        show-scrollbar="false"
        scroll-left="{{scrollLeft}}"
        bindscroll="onStationScroll"
      >
        <view class="stations-wrapper">
          <!-- 水平连接线 -->
          <view class="connection-line" style="{{progressStyle}}"></view>

          <!-- 站点列表 -->
          <view
            class="station-item {{item.status}} {{item.status === 'waiting' ? 'clickable' : ''}} {{index === targetStationIndex ? 'target-station' : ''}}"
            wx:for="{{stations}}"
            wx:key="id"
            data-index="{{index}}"
            bindtap="onStationTap"
          >
            <!-- 站点圆点（序号在中心） -->
            <view class="station-dot">
              <!-- 站点序号在圆圈中心 -->
              <text class="station-number-center">{{index + 1}}</text>
              <!-- 班车图标（仅在当前站点显示） -->
              <view class="bus-marker" wx:if="{{item.status === 'current'}}">
                <text class="bus-icon-small">🚌</text>
              </view>
              <!-- 到站标记（当班车在50米范围内时显示） -->
              <view class="arrived-marker" wx:if="{{item.status === 'current' && busLocation.isAtStation}}">
                <text class="arrived-icon">✅</text>
              </view>
            </view>

            <!-- 动画小班车（在当前站点显示） -->
            <view class="moving-bus" wx:if="{{item.status === 'current'}}">
              <text class="bus-icon-moving">🚌</text>
            </view>

            <!-- 站点信息 -->
            <view class="station-info">
              <text class="station-name">{{item.name}}</text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 操作提示 -->
    <view class="tips-section">
      <view class="tip-item">
        <text class="tip-icon">👆</text>
        <text class="tip-text">点击未到达站点查看预估时间</text>
      </view>
      <view class="tip-item">
        <text class="tip-icon">📍</text>
        <text class="tip-text">班车位置实时更新</text>
      </view>
    </view>
  </view>
</view>

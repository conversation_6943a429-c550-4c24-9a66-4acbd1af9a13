/* pages/notice/notice.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.notice-content {
  background: #fff;
  border-radius: 20rpx;
  padding: 80rpx 60rpx;
  text-align: center;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
  max-width: 600rpx;
  width: 100%;
}

.icon-container {
  margin-bottom: 40rpx;
}

.notice-icon {
  font-size: 120rpx;
  display: block;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20rpx);
  }
  60% {
    transform: translateY(-10rpx);
  }
}

.notice-text {
  margin-bottom: 40rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  font-size: 32rpx;
  color: #4ECDC4;
  font-weight: 500;
}

.description {
  margin-top: 40rpx;
}

.desc-text {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 10rpx;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .notice-content {
    padding: 60rpx 40rpx;
  }
  
  .notice-icon {
    font-size: 100rpx;
  }
  
  .title {
    font-size: 42rpx;
  }
  
  .subtitle {
    font-size: 28rpx;
  }
  
  .desc-text {
    font-size: 26rpx;
  }
}

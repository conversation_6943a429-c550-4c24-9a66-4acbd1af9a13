namespace WeChatBus.Configuration;

/// <summary>
/// GPS API配置
/// </summary>
public class GpsApiConfiguration
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public const string SectionName = "GpsApi";

    /// <summary>
    /// API基础URL
    /// </summary>
    public string BaseUrl { get; set; } = string.Empty;

    /// <summary>
    /// API路径
    /// </summary>
    public string ApiPath { get; set; } = string.Empty;

    /// <summary>
    /// 授权令牌
    /// </summary>
    public string Authorization { get; set; } = string.Empty;

    /// <summary>
    /// 车辆ID列表
    /// </summary>
    public List<string> CarIds { get; set; } = new();

    /// <summary>
    /// 请求超时时间（秒）
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 3;

    /// <summary>
    /// 重试延迟（毫秒）
    /// </summary>
    public int RetryDelayMs { get; set; } = 1000;
}

/// <summary>
/// 数据同步配置
/// </summary>
public class DataSyncConfiguration
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public const string SectionName = "DataSync";

    /// <summary>
    /// 同步间隔（秒）
    /// </summary>
    public int IntervalSeconds { get; set; } = 10;

    /// <summary>
    /// 是否启用自动同步
    /// </summary>
    public bool EnableAutoSync { get; set; } = true;

    /// <summary>
    /// 最大并发请求数
    /// </summary>
    public int MaxConcurrentRequests { get; set; } = 5;
}

/// <summary>
/// Redis配置
/// </summary>
public class RedisConfiguration
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public const string SectionName = "Redis";

    /// <summary>
    /// 频道前缀
    /// </summary>
    public string ChannelPrefix { get; set; } = "wechat_bus:";

    /// <summary>
    /// 位置更新频道
    /// </summary>
    public string LocationUpdateChannel { get; set; } = "location_update";

    /// <summary>
    /// 键过期时间（分钟）
    /// </summary>
    public int KeyExpirationMinutes { get; set; } = 30;

    /// <summary>
    /// 获取完整频道名称
    /// </summary>
    public string GetFullChannelName(string channel)
    {
        return $"{ChannelPrefix}{channel}";
    }
}

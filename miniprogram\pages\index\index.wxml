<!--pages/index/index.wxml-->
<view class="container">
  <!-- 简洁头部 -->
  <view class="header">
    <view class="title">服务时间：6:00-9:00 </view>
  </view>

  <!-- 内容区域 -->
  <view class="content">
    <!-- 加载状态 -->
    <view class="loading" wx:if="{{isLoading}}">
      <view class="loading-icon">🚌</view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 错误状态 -->
    <view class="error-state" wx:elif="{{hasError}}">
      <view class="error-icon">⚠️</view>
      <view class="error-content">
        <text class="error-title">{{errorMessage}}</text>
        <text class="error-desc">请检查网络连接或稍后重试</text>
        <button class="retry-btn" wx:if="{{showRetry}}" bindtap="retryLoad">重新加载</button>
      </view>
    </view>

    <!-- 班车列表 -->
    <view class="bus-list" wx:else>
      <view wx:if="{{routes.length === 0}}" class="empty-state">
        <view class="empty-icon">🚌</view>
        <text class="empty-text">暂无班车数据</text>
        <text class="empty-desc">请稍后刷新查看</text>
      </view>
      <view
        class="bus-item {{!isInTimeRange ? 'disabled' : ''}} {{!item.isActive || item.status === 'offline' ? 'offline' : ''}}"
        wx:for="{{routes}}"
        wx:key="id"
        bindtap="onRouteClick"
        data-route="{{item}}"
        style="border-left: 4rpx solid {{item.color}}"
      >
        <view class="bus-icon {{!item.isActive || item.status === 'offline' ? 'offline' : ''}}" style="background-color: {{item.color}}">
          <text class="icon-text">🚌</text>
        </view>
        <view class="bus-content">
          <view class="bus-info">
            <view class="bus-name {{!item.isActive || item.status === 'offline' ? 'offline' : ''}}">{{item.name}}</view>
            <view class="bus-car {{!item.isActive || item.status === 'offline' ? 'offline' : ''}}">{{item.carNumber}}</view>
            <!-- 实时位置信息（仅在服务时间内且车辆在线时显示） -->
            <view class="bus-location" wx:if="{{isInTimeRange && item.isActive && item.status !== 'offline' && item.isDetailEnabled && item.nextStation}}">
              <view class="next-station-info">
                <text class="next-text">正在前往</text>
                <text class="next-station">【{{item.nextStation}}】</text>
              </view>
            </view>
            <!-- 离线状态显示 -->
            <view class="bus-location offline-info" wx:if="{{!item.isActive || item.status === 'offline'}}">
              <text class="offline-text">班车离线</text>
            </view>
          </view>
          <view class="bus-status">
            <view class="status-dot {{item.isActive && item.status !== 'offline' ? 'online' : 'offline'}}"></view>
            <text class="status-text {{!item.isActive || item.status === 'offline' ? 'offline' : ''}}">{{item.statusText || (item.isActive ? '在线' : '离线')}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>


</view>

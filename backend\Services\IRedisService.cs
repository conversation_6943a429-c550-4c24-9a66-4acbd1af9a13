using WeChatBus.Models;

namespace WeChatBus.Services;

/// <summary>
/// Redis数据服务接口
/// </summary>
public interface IRedisService
{
    /// <summary>
    /// 保存车辆位置信息
    /// </summary>
    /// <param name="locations">车辆位置信息列表</param>
    Task SaveVehicleLocationsAsync(List<VehicleLocation> locations);

    /// <summary>
    /// 保存单个车辆位置信息
    /// </summary>
    /// <param name="location">车辆位置信息</param>
    Task SaveVehicleLocationAsync(VehicleLocation location);

    /// <summary>
    /// 获取车辆位置信息
    /// </summary>
    /// <param name="carId">车辆ID</param>
    /// <returns>车辆位置信息</returns>
    Task<VehicleLocation?> GetVehicleLocationAsync(string carId);

    /// <summary>
    /// 获取所有车辆位置信息
    /// </summary>
    /// <returns>车辆位置信息列表</returns>
    Task<List<VehicleLocation>> GetAllVehicleLocationsAsync();

    /// <summary>
    /// 发布位置更新事件
    /// </summary>
    /// <param name="updateEvent">位置更新事件</param>
    Task PublishLocationUpdateAsync(LocationUpdateEvent updateEvent);

    /// <summary>
    /// 订阅位置更新事件
    /// </summary>
    /// <param name="onMessage">消息处理回调</param>
    Task SubscribeLocationUpdatesAsync(Action<string, string> onMessage);

    /// <summary>
    /// 检查Redis连接状态
    /// </summary>
    /// <returns>连接是否正常</returns>
    Task<bool> IsConnectedAsync();

    /// <summary>
    /// 清理过期数据
    /// </summary>
    Task CleanupExpiredDataAsync();

    /// <summary>
    /// 保存到站状态到缓存（有效期10小时）
    /// </summary>
    /// <param name="carId">车辆ID</param>
    /// <param name="stationId">站点ID</param>
    /// <param name="arrivalTime">到站时间</param>
    Task SaveStationArrivalAsync(string carId, string stationId, DateTime arrivalTime);

    /// <summary>
    /// 获取车辆到站历史记录
    /// </summary>
    /// <param name="carId">车辆ID</param>
    /// <returns>到站历史记录</returns>
    Task<List<StationArrival>> GetStationArrivalsAsync(string carId);

    /// <summary>
    /// 检查车辆是否在指定时间内到过某站
    /// </summary>
    /// <param name="carId">车辆ID</param>
    /// <param name="stationId">站点ID</param>
    /// <param name="withinHours">时间范围（小时）</param>
    /// <returns>是否到过该站</returns>
    Task<bool> HasArrivedAtStationAsync(string carId, string stationId, int withinHours = 1);
}
